/**
* @File Name : EM015_UploadFilesOperationCEETest
* @Description : Classe de test pour EM015_UploadFilesOperationCEE
* <AUTHOR> Assistant IA
* @Last Modified By :
* @Last Modified On : July 28, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 28, 2025|   | Initial Version
**/
@isTest
private class EM015_UploadFilesOperationCEETest {

    /**
    * @Description      : Test de la méthode getDocumentsByFicheCEEAndTypeClient avec des paramètres valides
    * @method Name      : testGetDocumentsByFicheCEEAndTypeClient_WithValidParameters
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetDocumentsByFicheCEEAndTypeClient_WithValidParameters() {
        Test.startTest();
        
        // Récupération d'un enregistrement de Custom Metadata Type existant pour les tests
        List<UploadFilesOperationCEE__mdt> existingRecords = [
            SELECT Id, FicheCEE__c, typeClient__c, typePersonne__c, typeCategorie__c
            FROM UploadFilesOperationCEE__mdt 
            LIMIT 1
        ];
        
        if (!existingRecords.isEmpty()) {
            UploadFilesOperationCEE__mdt testRecord = existingRecords[0];
            
            // Appel de la méthode à tester avec les valeurs de l'enregistrement existant
            List<UploadFilesOperationCEE__mdt> result = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
                testRecord.FicheCEE__c,
                testRecord.typeClient__c,
                testRecord.typePersonne__c,
                testRecord.typeCategorie__c
            );
            
            // Vérifications
            System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
            System.assert(result.size() >= 0, 'Le résultat devrait être une liste valide');
            
            // Vérifier que tous les résultats correspondent aux critères
            for (UploadFilesOperationCEE__mdt record : result) {
                System.assertEquals(testRecord.FicheCEE__c, record.FicheCEE__c, 
                    'FicheCEE__c devrait correspondre au paramètre');
                System.assert(
                    record.typeClient__c == testRecord.typeClient__c || record.typeClient__c == 'client',
                    'typeClient__c devrait correspondre au paramètre ou être "client"'
                );
            }
        } else {
            // Si aucun enregistrement n'existe, tester avec des valeurs fictives
            List<UploadFilesOperationCEE__mdt> result = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
                'TestFiche',
                'TestClient',
                'TestPersonne',
                'TestCategorie'
            );
            
            System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
            System.assertEquals(0, result.size(), 'Aucun résultat attendu pour des valeurs fictives');
        }
        
        Test.stopTest();
    }

    /**
    * @Description      : Test de la méthode getDocumentsByFicheCEEAndTypeClient avec des paramètres null
    * @method Name      : testGetDocumentsByFicheCEEAndTypeClient_WithNullParameters
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetDocumentsByFicheCEEAndTypeClient_WithNullParameters() {
        Test.startTest();
        
        // Test avec FicheCEE null
        List<UploadFilesOperationCEE__mdt> result1 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            null,
            'TestClient',
            'TestPersonne',
            'TestCategorie'
        );
        
        // Test avec TypeClient null
        List<UploadFilesOperationCEE__mdt> result2 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            'TestFiche',
            null,
            'TestPersonne',
            'TestCategorie'
        );
        
        // Test avec TypePersonne null (devrait fonctionner car géré dans la logique)
        List<UploadFilesOperationCEE__mdt> result3 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            'TestFiche',
            'TestClient',
            null,
            'TestCategorie'
        );
        
        // Test avec Categorie null (devrait fonctionner car géré dans la logique)
        List<UploadFilesOperationCEE__mdt> result4 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            'TestFiche',
            'TestClient',
            'TestPersonne',
            null
        );
        
        // Test avec tous les paramètres null
        List<UploadFilesOperationCEE__mdt> result5 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            null,
            null,
            null,
            null
        );
        
        Test.stopTest();
        
        // Vérifications
        System.assertNotEquals(null, result1, 'Le résultat 1 ne devrait pas être null');
        System.assertNotEquals(null, result2, 'Le résultat 2 ne devrait pas être null');
        System.assertNotEquals(null, result3, 'Le résultat 3 ne devrait pas être null');
        System.assertNotEquals(null, result4, 'Le résultat 4 ne devrait pas être null');
        System.assertNotEquals(null, result5, 'Le résultat 5 ne devrait pas être null');
        
        // Les résultats devraient être vides pour les paramètres null critiques
        System.assertEquals(0, result1.size(), 'Aucun résultat attendu avec FicheCEE null');
        System.assertEquals(0, result5.size(), 'Aucun résultat attendu avec tous les paramètres null');
    }

    /**
    * @Description      : Test de la méthode getDocumentsByFicheCEEAndTypeClient avec des paramètres vides
    * @method Name      : testGetDocumentsByFicheCEEAndTypeClient_WithEmptyParameters
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetDocumentsByFicheCEEAndTypeClient_WithEmptyParameters() {
        Test.startTest();
        
        // Test avec des chaînes vides
        List<UploadFilesOperationCEE__mdt> result1 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            '',
            '',
            '',
            ''
        );
        
        // Test avec des espaces
        List<UploadFilesOperationCEE__mdt> result2 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            '   ',
            '   ',
            '   ',
            '   '
        );
        
        Test.stopTest();
        
        // Vérifications
        System.assertNotEquals(null, result1, 'Le résultat 1 ne devrait pas être null');
        System.assertNotEquals(null, result2, 'Le résultat 2 ne devrait pas être null');
        
        // Les résultats devraient être vides pour des paramètres vides
        System.assertEquals(0, result1.size(), 'Aucun résultat attendu avec des paramètres vides');
        System.assertEquals(0, result2.size(), 'Aucun résultat attendu avec des espaces');
    }

    /**
    * @Description      : Test de la méthode getDocumentsByFicheCEEAndTypeClient avec typeClient = 'client'
    * @method Name      : testGetDocumentsByFicheCEEAndTypeClient_WithClientType
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetDocumentsByFicheCEEAndTypeClient_WithClientType() {
        Test.startTest();
        
        // Récupération d'enregistrements avec typeClient__c = 'client' s'ils existent
        List<UploadFilesOperationCEE__mdt> clientRecords = [
            SELECT Id, FicheCEE__c, typeClient__c, typePersonne__c, typeCategorie__c
            FROM UploadFilesOperationCEE__mdt 
            WHERE typeClient__c = 'client'
            LIMIT 1
        ];
        
        if (!clientRecords.isEmpty()) {
            UploadFilesOperationCEE__mdt clientRecord = clientRecords[0];
            
            // Test avec un typeClient différent de 'client' mais qui devrait quand même retourner les enregistrements 'client'
            List<UploadFilesOperationCEE__mdt> result = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
                clientRecord.FicheCEE__c,
                'AutreTypeClient',
                clientRecord.typePersonne__c,
                clientRecord.typeCategorie__c
            );
            
            // Vérifications
            System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
            
            // Vérifier que les enregistrements 'client' sont inclus
            Boolean hasClientRecord = false;
            for (UploadFilesOperationCEE__mdt record : result) {
                if (record.typeClient__c == 'client') {
                    hasClientRecord = true;
                    break;
                }
            }
            
            System.assert(hasClientRecord, 'Les enregistrements avec typeClient__c = "client" devraient être inclus');
        }
        
        Test.stopTest();
    }

    /**
    * @Description      : Test de la logique de filtrage pour TypePersonne et Categorie
    * @method Name      : testGetDocumentsByFicheCEEAndTypeClient_FilteringLogic
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetDocumentsByFicheCEEAndTypeClient_FilteringLogic() {
        Test.startTest();
        
        // Récupération d'un enregistrement existant pour tester la logique de filtrage
        List<UploadFilesOperationCEE__mdt> existingRecords = [
            SELECT Id, FicheCEE__c, typeClient__c, typePersonne__c, typeCategorie__c
            FROM UploadFilesOperationCEE__mdt 
            WHERE typePersonne__c != null OR typeCategorie__c != null
            LIMIT 1
        ];
        
        if (!existingRecords.isEmpty()) {
            UploadFilesOperationCEE__mdt testRecord = existingRecords[0];
            
            // Test avec TypePersonne et Categorie correspondants
            List<UploadFilesOperationCEE__mdt> result1 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
                testRecord.FicheCEE__c,
                testRecord.typeClient__c,
                testRecord.typePersonne__c,
                testRecord.typeCategorie__c
            );
            
            // Test avec TypePersonne et Categorie null (devrait inclure les enregistrements avec ces champs null)
            List<UploadFilesOperationCEE__mdt> result2 = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
                testRecord.FicheCEE__c,
                testRecord.typeClient__c,
                null,
                null
            );
            
            // Vérifications
            System.assertNotEquals(null, result1, 'Le résultat 1 ne devrait pas être null');
            System.assertNotEquals(null, result2, 'Le résultat 2 ne devrait pas être null');
            
            // result2 devrait contenir au moins autant d'enregistrements que result1
            System.assert(result2.size() >= result1.size(), 
                'Le filtrage avec null devrait retourner au moins autant d\'enregistrements');
        }
        
        Test.stopTest();
    }

    /**
    * @Description      : Test direct de la méthode avec des valeurs connues
    * @method Name      : testGetDocumentsByFicheCEEAndTypeClient_DirectTest
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetDocumentsByFicheCEEAndTypeClient_DirectTest() {
        Test.startTest();

        // Test direct de la méthode - vérifie que la méthode ne lève pas d'exception
        try {
            List<UploadFilesOperationCEE__mdt> result = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
                'TestFiche',
                'TestClient',
                'TestPersonne',
                'TestCategorie'
            );

            // Vérifications de base
            System.assertNotEquals(null, result, 'Le résultat ne devrait jamais être null');
            System.assert(result instanceof List<UploadFilesOperationCEE__mdt>,
                'Le résultat devrait être une liste de UploadFilesOperationCEE__mdt');

        } catch (Exception e) {
            System.assert(false, 'La méthode ne devrait pas lever d\'exception : ' + e.getMessage());
        }

        Test.stopTest();
    }

    /**
    * @Description      : Test de performance et de limites
    * @method Name      : testGetDocumentsByFicheCEEAndTypeClient_Performance
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetDocumentsByFicheCEEAndTypeClient_Performance() {
        Test.startTest();

        // Test avec des chaînes très longues pour vérifier la robustesse
        String longString = 'A'.repeat(255); // Chaîne de 255 caractères

        List<UploadFilesOperationCEE__mdt> result = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            longString,
            longString,
            longString,
            longString
        );

        // Vérifications
        System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null même avec des chaînes longues');
        System.assertEquals(0, result.size(), 'Aucun résultat attendu pour des chaînes très longues');

        Test.stopTest();
    }
}
