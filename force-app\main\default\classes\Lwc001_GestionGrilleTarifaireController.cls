/**
 * @File Name : Lwc001_GestionGrilleTarifaireController
 * @Description : CONTROLLEUR DU COMPOSANT Lwc001_GestionGrilleTarifaire - Version avec grilles et lignes
 * <AUTHOR> Amina BENABDELKRIM
 * @Last Modified By : 
 * @Last Modified On : JULY 03, 2025
 * @Modification Log :
 *==============================================================================
 * Ver | Date | Author | Modification
 *==============================================================================
 * 1.0 | JULY 03, 2025 |   | Version initiale avec grilles et lignes
 **/
public with sharing class Lwc001_GestionGrilleTarifaireController {
    
    /**
     * @description Récupèrer toutes les grilles tarifaires avec leurs lignes pour un contrat
     * @param contractId ID du contrat
     * @return List<GrilleTarifaire__c>
     */
    @AuraEnabled(cacheable=true)
    public static List<GrilleTarifaire__c> getGrillesTarifaires(Id contractId) {
        try {
            System.debug('contractId ==> ' + contractId);
            
            if (contractId == null) {
                throw new IllegalArgumentException('ID du contrat est null');
            }
            
            List<GrilleTarifaire__c> grilles = EM007_GrilleTarifaire.getGrilleTarifaireByConvention(contractId);
            
            System.debug('Nombre de grilles trouvées: ' + grilles.size());
            for (GrilleTarifaire__c grille : grilles) {
                System.debug('Grille ' + grille.Id + ' a ' + grille.LigneGrilleTarifiare__r.size() + ' lignes');
            }
            
            return grilles;
            
        } catch (Exception e) {
            System.debug('ERREUR dans getGrillesTarifaires: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw new AuraHandledException('Erreur lors de la récupération des grilles: ' + e.getMessage());
        }
    }
    
    /**
     * @description Créer une nouvelle grille tarifaire
     * @param grille Données de la grille à créer
     * @return GrilleTarifaire__c
     */
    @AuraEnabled
    public static GrilleTarifaire__c createGrilleTarifaire(GrilleTarifaire__c grille) {
        try {
            System.debug('=== CREATE GRILLE TARIFAIRE ===');
            System.debug('Grille à créer: ' + grille);
            
            if (grille == null) {
                throw new IllegalArgumentException('Données de la grille requises');
            }
            
          
            insert grille;
            
            System.debug('Grille créée avec succès: ' + grille.Id);
            return grille;
            
        } catch (Exception e) {
            System.debug('ERREUR dans createGrilleTarifaire: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            
            if (e instanceof IllegalArgumentException) {
                throw new AuraHandledException(e.getMessage());
            } else {
                throw new AuraHandledException('Erreur lors de la création de la grille: ' + e.getMessage());
            }
        }
    }
    
    /**
     * @description Sauvegarder les lignes tarifaires pour une grille
     * @param lignes Liste des lignes à sauvegarder
     * @param grilleId ID de la grille tarifaire
     * @return List<LigneGrilleTarifiare__c>
     */
    @AuraEnabled
    public static List<LigneGrilleTarifiare__c> saveLignesTarifaires(List<LigneGrilleTarifiare__c> lignes, Id grilleId) {
        try {
            System.debug('=== SAVE LIGNES TARIFAIRES ===');
            System.debug('Nombre de lignes à sauvegarder: ' + lignes.size());
            System.debug('GrilleId: ' + grilleId);
            
            if (lignes == null || lignes.isEmpty()) {
                throw new IllegalArgumentException('Aucune ligne à sauvegarder');
            }
            
            if (grilleId == null) {
                throw new IllegalArgumentException('ID de la grille tarifaire requis');
            }
            
           
            
            insert lignes;
            
            System.debug('Lignes sauvegardées avec succès');
            return lignes;
            
        } catch (Exception e) {
            System.debug('ERREUR dans saveLignesTarifaires: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            
            if (e instanceof IllegalArgumentException) {
                throw new AuraHandledException(e.getMessage());
            } else {
                throw new AuraHandledException('Erreur lors de la sauvegarde des lignes: ' + e.getMessage());
            }
        }
    }
    
    
    /**
     * @description Supprimer une grille tarifaire et toutes ses lignes
     * @param grilleId ID de la grille à supprimer
     */
    @AuraEnabled
    public static void supprimerGrilleTarifaire(Id grilleId) {
        try {
            System.debug('=== SUPPRIMER GRILLE TARIFAIRE ===');
            System.debug('GrilleId: ' + grilleId);
            
            if (grilleId == null) {
                throw new IllegalArgumentException('ID de la grille tarifaire requis');
            }
            
            GrilleTarifaire__c grille = EM007_GrilleTarifaire.getGrilleTarifaireById(grilleId);
            
            if (grille.statutGrille__c == 'Validée') {
                throw new IllegalArgumentException('Impossible de supprimer une grille tarifaire validée');
            }
            
            if (!grille.LigneGrilleTarifiare__r.isEmpty()) {
                delete grille.LigneGrilleTarifiare__r;
            }
            
            delete grille;
            
            System.debug('Grille supprimée avec succès');
            
        } catch (Exception e) {
            System.debug('ERREUR dans supprimerGrilleTarifaire: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            
            if (e instanceof IllegalArgumentException) {
                throw new AuraHandledException(e.getMessage());
            } else {
                throw new AuraHandledException('Erreur lors de la suppression de la grille: ' + e.getMessage());
            }
        }
    }

     
    @AuraEnabled
    public static void updateGrilleDateFin(Id grilleId, Date nouvelleDateFin) {
        try {
            System.debug('=== UPDATE GRILLE DATE FIN ===');
            System.debug('GrilleId: ' + grilleId + ', Nouvelle date fin: ' + nouvelleDateFin);
            
            if (grilleId == null) {
                throw new IllegalArgumentException('ID de la grille tarifaire requis');
            }
            
            if (nouvelleDateFin == null) {
                throw new IllegalArgumentException('Nouvelle date de fin requise');
            }
            
            GrilleTarifaire__c grille = EM007_GrilleTarifaire.getGrilleTarifaireById(grilleId);
            
            if (nouvelleDateFin <= grille.dateDebut__c) {
                throw new IllegalArgumentException('La nouvelle date de fin doit être supérieure à la date de début');
            }
            
            grille.dateFin__c = nouvelleDateFin;
            update grille;
            
            System.debug('Date de fin mise à jour avec succès');
            
        } catch (Exception e) {
            System.debug('ERREUR dans updateGrilleDateFin: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            
            if (e instanceof IllegalArgumentException) {
                throw new AuraHandledException(e.getMessage());
            } else {
                throw new AuraHandledException('Erreur lors de la mise à jour de la date de fin: ' + e.getMessage());
            }
        }
    }

    /**
     * @description Mettre à jour une ligne tarifaire (prix seulement)
     * @param ligne Ligne à mettre à jour
     * @return LigneGrilleTarifiare__c
     */
    @AuraEnabled
    public static LigneGrilleTarifiare__c updateLigneTarifaire(LigneGrilleTarifiare__c ligne) {
        try {
            System.debug('=== UPDATE LIGNE TARIFAIRE ===');
            System.debug('Ligne à mettre à jour: ' + ligne);
            
            if (ligne == null || ligne.Id == null) {
                throw new IllegalArgumentException('Ligne et ID requis');
            }
            
            LigneGrilleTarifiare__c ligneToUpdate = new LigneGrilleTarifiare__c(
                Id = ligne.Id,
                PrixClassique__c = ligne.PrixClassique__c,
                PrixPrecaire__c = ligne.PrixPrecaire__c
            );
            
            update ligneToUpdate;
            
            System.debug('Ligne mise à jour avec succès');
            return ligneToUpdate;
            
        } catch (Exception e) {
            System.debug('ERREUR dans updateLigneTarifaire: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            
            if (e instanceof IllegalArgumentException) {
                throw new AuraHandledException(e.getMessage());
            } else {
                throw new AuraHandledException('Erreur lors de la mise à jour de la ligne: ' + e.getMessage());
            }
        }
    }

    /**
     * @description Supprimer toutes les lignes d'une grille tarifaire
     * @param grilleId ID de la grille tarifaire
     */
    @AuraEnabled
    public static void supprimerToutesLignes(Id grilleId) {
        try {
            System.debug('=== SUPPRIMER TOUTES LES LIGNES ===');
            System.debug('GrilleId: ' + grilleId);
            
            if (grilleId == null) {
                throw new IllegalArgumentException('ID de la grille tarifaire requis');
            }
            
            List<LigneGrilleTarifiare__c> lignes = EM012_LigneGrilleTarifaire.getLigneGrilleByGrille(grilleId);
            
            if (!lignes.isEmpty()) {
                delete lignes;
                System.debug('Toutes les lignes supprimées: ' + lignes.size());
            }
            
        } catch (Exception e) {
            System.debug('ERREUR dans supprimerToutesLignes: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            
            if (e instanceof IllegalArgumentException) {
                throw new AuraHandledException(e.getMessage());
            } else {
                throw new AuraHandledException('Erreur lors de la suppression des lignes: ' + e.getMessage());
            }
        }
    }
    
  
}