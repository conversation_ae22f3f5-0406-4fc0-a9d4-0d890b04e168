/**
 * @File Name : Lwc001_GestionGrilleTarifaireController
 * @Description : CONTROLLEUR DU COMPOSANT Lwc001_GestionGrilleTarifaire
 * <AUTHOR> BENABDELKRIM
 * @Last Modified By : 
 * @Last Modified On : JULY 03, 2025
 * @Modification Log :
 *==============================================================================
 * Ver | Date | Author | Modification
 *==============================================================================
 * 1.0 | JULY 03, 2025 |   | Initial Version
 * 1.1 | JULY 03, 2025 |   | Ajout suppression en masse et modification
 **/
public with sharing class Lwc001_GestionGrilleTarifaireController {
    
    /**
     * @description Récupèrer toutes les grilles tarifaires liées à un avenant
     * @param avenantId ID avenant
     * @return List<GrilleTarifaire__c>
     */
    @AuraEnabled(cacheable=true)
    public static List<GrilleTarifaire__c> getGrillesTarifaires(Id avenantId) {
        try {
            System.debug('avenantId ==> ' + avenantId);
            
            if (avenantId == null) {
                throw new IllegalArgumentException('ID de l\'avenant est null');
            }
            List<GrilleTarifaire__c> grilles = EM007_GrilleTarifaire.getGrilleTarifaireByConvention(avenantId);
            return grilles;
            
        } catch (Exception e) {
            System.debug('ERREUR dans getGrillesTarifaires: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw e;
        }
    }
    
    /**
     * @description Sauvegarder les grilles tarifaires
     * @param grilles Liste des grilles à sauvegarder
     * @param avenantId ID avenant
     * @return List<GrilleTarifaire__c> 
     */
    @AuraEnabled
    public static List<GrilleTarifaire__c> saveGrillesTarifaires(List<GrilleTarifaire__c> grilles) {
        try {
            System.debug('=== SAVE GRILLES TARIFAIRES ===');
            
            if (grilles == null || grilles.isEmpty()) {
                throw new IllegalArgumentException('Aucune grille à sauvegarder');
            }
            insert grilles;
            
            return grilles;
            
        } catch (Exception e) {
            System.debug('ERREUR dans saveGrillesTarifaires: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw e;
        }
    }
    
    /**
     * @description Supprimer une grille tarifaire
     * @param grilleId 
     */
    @AuraEnabled
    public static void deleteGrilleTarifaire(Id grilleId) {
        try {
            System.debug('grilleId: ' + grilleId);
            
            if (grilleId == null) {
                throw new IllegalArgumentException('ID de la grille requis');
            }
            
            GrilleTarifaire__c grille = EM007_GrilleTarifaire.getGrilleTarifaireById(grilleId);
            delete grille;
            
        } catch (Exception e) {
            System.debug('ERREUR dans deleteGrilleTarifaire: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw e;
        }
    }
    
    /**
     * @description Supprimer toutes les grilles tarifaires d'une convention
     * @param avenantId ID de la convention
     */
    @AuraEnabled
    public static void deleteAllGrillesTarifaires(Id avenantId) {
        try {
            System.debug('=== DELETE ALL GRILLES TARIFAIRES ===');
            System.debug('avenantId: ' + avenantId);
            
            if (avenantId == null) {
                throw new IllegalArgumentException('ID de la convention requis');
            }
            
            List<GrilleTarifaire__c> grillesToDelete = EM007_GrilleTarifaire.getGrilleTarifaireByConvention(avenantId);
            
            if (!grillesToDelete.isEmpty()) {
                delete grillesToDelete;
                System.debug('Nombre de grilles supprimées: ' + grillesToDelete.size());
            }
            
        } catch (Exception e) {
            System.debug('ERREUR dans deleteAllGrillesTarifaires: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw e;
        }
    }
    
    /**
     * @description Mettre à jour une grille tarifaire (modification des prix uniquement)
     * @param grille Grille à mettre à jour
     * @return GrilleTarifaire__c
     */
    @AuraEnabled
    public static GrilleTarifaire__c updateGrilleTarifaire(GrilleTarifaire__c grille) {
        try {
            System.debug('=== UPDATE GRILLE TARIFAIRE ===');
            System.debug('Grille à modifier: ' + grille.Id);
            
            if (grille == null || grille.Id == null) {
                throw new IllegalArgumentException('Grille tarifaire et ID requis');
            }
            
            // Récupérer la grille existante pour vérifier qu'elle existe
            GrilleTarifaire__c existingGrille = EM007_GrilleTarifaire.getGrilleTarifaireById(grille.Id);
            
            if (existingGrille == null) {
                throw new IllegalArgumentException('Grille tarifaire non trouvée');
            }
            
            // Mettre à jour uniquement les prix (pas les valeurs De et À)
            existingGrille.PrixClassique__c = grille.PrixClassique__c;
            existingGrille.PrixPrecaire__c = grille.PrixPrecaire__c;
            
            update existingGrille;
            
            System.debug('Grille mise à jour avec succès');
            return existingGrille;
            
        } catch (Exception e) {
            System.debug('ERREUR dans updateGrilleTarifaire: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw e;
        }
    }
}