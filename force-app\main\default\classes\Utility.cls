/**
* @File Name : Utility 
* @Description : Utility
* <AUTHOR> <PERSON><PERSON>LKRIM
* @Last Modified By : <PERSON>
* @Last Modified On : JULY 27, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | JULY 05, 2025 |   | Initial Version
**/
public with sharing class Utility {

   public static String generateRandomString(Integer length) {
        final String chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        String result = '';
        for (Integer i = 0; i < length; i++) {
            Integer idx = Math.mod(Math.abs(Crypto.getRandomInteger()), chars.length());
            result += chars.substring(idx, idx + 1);
        }
        return result;
    }

    public static String generateNumberString(Integer length) {
        final String chars = '0123456789';
        String result = '';
        for (Integer i = 0; i < length; i++) {
            Integer idx = Math.mod(Math.abs(Crypto.getRandomInteger()), chars.length());
            result += chars.substring(idx, idx + 1);
        }
        return result;
    }
    public static void assertExpectedIdsInResults(List<sObject> expectedList, List<sObject> resultList){
        Set<Id> expectedIds = new Set<Id>();
        for (sObject val : expectedList) {
            expectedIds.add(val.Id);
        }
        System.assertNotEquals(null, resultList, 'La liste de résultats ne doit pas être null');
        System.assertNotEquals(null, expectedList, 'La liste attendue ne doit pas être null');
        System.assertEquals(expectedIds.size(), resultList.size(), 'Le nombre de résultats ne correspond pas au nombre attendu');
        for (sObject res : resultList) {
            System.assert(expectedIds.contains(res.Id), 'Le résultat contient un enregistrement inattendu : ' + res.Id);
        }
    }
  
}