/**
* @File Name : DM006_Contract
* @Description : DM de l'objet Contract
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 10, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 10, 2025|   | Initial Version
**/
public with sharing class DM006_Contract {
    /**
    * @Description      :   Cette methode permet récupérer Le contract selon la covention originale
    * @method Name      :   selectContractByConventionOriginal
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   originalConventionId => Id de la convention originale
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<Contract> selectContractByConventionOriginal(Id originalConventionId){
          return [ SELECT Id From Contract WHERE  ConventionOriginal__c =:originalConventionId ORDER BY CreatedDate DESC];     
    }

     /**
    * @Description      :   Cette methode permet récupérer Le contract par son Id
    * @method Name      :   selectContractById
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   idContract => Id du contract
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<Contract> selectContractById(Id idContract){
          return [ SELECT Id,Name,DocumentContractuelInitiale__c,ContractNumber,ConventionOriginal__c,Version__c From Contract WHERE  Id =:idContract];     
    }

     /**
    * @Description      :   Cette methode permet récupérer La version du convention supérieur d'une convention atuelle
    * @method Name      :   selecthigherVersionofContract
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   idContract => Id du contract
    * @Param            :   currentVersion => version
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<Contract> selecthigherVersionOfContract(Id originalConventionId,Decimal currentVersion){
          return [SELECT Id, Version__c, Name, Status
                FROM Contract 
                WHERE (ConventionOriginal__c = :originalConventionId OR Id = :originalConventionId)
                AND Version__c > :currentVersion
                AND Status != 'Annulée'
                ORDER BY Version__c DESC
          ];
    }
}