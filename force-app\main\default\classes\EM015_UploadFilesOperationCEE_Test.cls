@isTest
public class EM015_UploadFilesOperationCEE_Test {
@isTest
static void EM015_UploadFilesOperationCEE_Test() {
        // Récupération d'un enregistrement de Custom Metadata Type existant
        List<UploadFilesOperationCEE__mdt> existingRecords = [
            SELECT Id, FicheCEE__c, typeClient__c, typePersonne__c, typeCategorie__c
            FROM UploadFilesOperationCEE__mdt 
            LIMIT 1
        ];

        // Vérifier que des données existent dans les Custom Metadata
        System.assert(!existingRecords.isEmpty(), 'Aucun enregistrement UploadFilesOperationCEE__mdt trouvé');

        UploadFilesOperationCEE__mdt record = existingRecords[0];

        // Appel de la méthode à tester
        List<UploadFilesOperationCEE__mdt> result = EM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(
            record.FicheCEE__c,
            record.typeClient__c,
            record.typePersonne__c,
            record.typeCategorie__c
        );

        // Vérification du résultat (au moins un résultat attendu si la méthode fonctionne)
        System.assert(result != null, 'Le résultat est null');
        System.assert(result.size() > 0, 'Aucun document retourné par getDocumentsByFicheCEEAndTypeClient');
        
        // Optionnel : vérification que les valeurs correspondent à la requête
        for (UploadFilesOperationCEE__mdt doc : result) {
            System.assertEquals(record.FicheCEE__c, doc.FicheCEE__c, 'FicheCEE__c ne correspond pas');
            System.assertEquals(record.typeClient__c, doc.typeClient__c, 'typeClient__c ne correspond pas');
            System.assertEquals(record.typePersonne__c, doc.typePersonne__c, 'typePersonne__c ne correspond pas');
            System.assertEquals(record.typeCategorie__c, doc.typeCategorie__c, 'typeCategorie__c ne correspond pas');
        }
    }
       

    

}