/**
 * @description       : 
 * <AUTHOR> 
 * @group             : 
 * @last modified on  : 15-07-2025
 * @last modified by  : 🦅  | DA-technologies
**/
global class SM003_ObjectDescriber {

    /**
    * <AUTHOR> Technologies
    * @description Datas Manager 000 - Gestion des requêtes sur tous les objets (sObjects)
    */

    global static final Map<String, Schema.SObjectType> schemaMap;

    global class Pair {
        public String key {get; set;}
        public String val {get; set;}
    }

    static {
        schemaMap = Schema.getGlobalDescribe();
    }

    /**
     * <AUTHOR> Technologies
     * @description Méthode permettant de retourner une liste de champs pour un objet donné
     * @param sObjectName String Nom du sObject
     * @return List<String> liste des champs du sObject en paramètre
     */
    global static List<String> getFieldsListFor(String sObjectName) {
    system.debug('## sObject : ' + sObjectName);
    System.debug('Im inside error above ');
    Map<String, Schema.SObjectField> fieldMap = schemaMap.get(sObjectName).getDescribe().fields.getMap();
    System.debug('Im inside error under ' + fieldMap);
    List<String> fieldsList = new List<String>();
    for (Schema.SObjectField sfield : fieldMap.Values()) {
        schema.describefieldresult dfield = sfield.getDescribe();
        String fieldName = dfield.getname();
        
        // Filtrer les champs qui ne correspondent pas à l'objet demandé
        if(sObjectName == 'Dossier__c' && fieldName.contains('NombreLogementsNonConventionnes')) {
            System.debug('SKIPPING WRONG FIELD: ' + fieldName + ' for object: ' + sObjectName);
            continue;
        }
        
        fieldsList.add(fieldName);
    }
    return fieldsList;
}

    /**
     * <AUTHOR> Technologies
     * @description Méthode permettant de retourner une liste de champs pour un objet donné
     * @param sObjectName String Nom du sObject
     * @return String liste des champs du sObject en paramètre
     */
    global static String constructFieldListForQueryFrom(String sObjectName) {
        return constructFilteredFieldListForQueryFrom(sObjectName, '');
    }

    /**
     * <AUTHOR> Technologies
     * @description Returns all fields EXCEPT those ending with the specified string 
     * @param sObjectName String Nom du sObject
     * @param notEndingFilter String specifies the ending name of the fields to omit
     * @return String liste des champs du sObject en paramètre
     */
    global static String constructFilteredFieldListForQueryFrom(String sObjectName, String notEndingFilter) {
    System.debug('sObjectName: ' + sObjectName);
    List<String> fieldsList = getFieldsListFor(sObjectName);
    String fieldsQuery = '';
    system.debug('## fieldsList : ' + fieldsList);
    for (String field : fieldsList) {
        if (String.isEmpty(notEndingFilter) || !field.endsWith(notEndingFilter)) {
            fieldsQuery = fieldsQuery + '' + field + ',';
        }
    }
    
    if (fieldsQuery.endsWith(',')) {
        fieldsQuery = fieldsQuery.substring(0, fieldsQuery.length() - 1);
    }
    
    return fieldsQuery;
}

    /**
    * <AUTHOR> Laila
    * @date Création 26/05/2020
    * @date Modification
    * @description
    */
    @SuppressWarnings('PMD.ApexSOQLInjection') // Suppressing warning as inputs are sanitized and validated
    public static List<sObject> lookUpSearch(String searchTerm, String objectName, String filters, String recordId, String fields) {
        Integer limitNum = 20;

        // Sanitize inputs
        objectName = String.escapeSingleQuotes(objectName);
        fields = String.escapeSingleQuotes(fields);
        filters = String.isNotBlank(filters) ? String.escapeSingleQuotes(filters) : null;

        String finalQuery = 'SELECT ' + fields + ' FROM ' + objectName;

        if (String.isBlank(recordId)) {
            if (String.isNotBlank(searchTerm)) {
                finalQuery += ' WHERE Name LIKE :searchTerm';
                searchTerm = '%' + searchTerm + '%'; // Use binding variable for searchTerm
            }

            if (String.isNotBlank(filters)) {
                finalQuery += String.isNotBlank(searchTerm) ? ' AND ' : ' WHERE ';
                finalQuery += filters;
            }
        } else {
            finalQuery += ' WHERE Id = :recordId'; // Use binding variable for recordId
        }

        finalQuery += ' LIMIT ' + limitNum;

        System.debug('Final Query::::' + finalQuery);

        // Execute the query
        List<sObject> lookUpList = Database.query(finalQuery);

        return lookUpList;
    }
    /**
     * Récupèrer tous les champs d'un objet dynamiquement
    */
    public static String buildDynamicQuery(String objectApiName,Id recordId) {
        try {
            String fieldsString = SM003_ObjectDescriber.constructFieldListForQueryFrom(objectApiName);
            
            if (String.isBlank(fieldsString)) {
                System.debug('Aucun champ trouvé pour l\'objet: ' + objectApiName);
                throw new System.CalloutException('Aucun champ trouvé pour l\'objet: ' + objectApiName);
            }
            
            String query = 'SELECT ' + fieldsString + ' FROM ' + objectApiName + ' WHERE Id = \'' + recordId + '\' LIMIT 1';
            
            return query;
            
        } catch (Exception e) {
            System.debug('Erreur lors de la construction de la requête: ' + e.getMessage());
            throw e;
        }
    }


    public static Map<String, String> getPicklistValues(String objectName, String fieldName){
        Map<String, String> pickListValuesList= new Map<String, String>();
        System.debug('objectName : '+objectName+' fieldName : '+fieldName);
        Schema.SObjectType s = Schema.getGlobalDescribe().get(objectName) ;
        Schema.DescribeSObjectResult r = s.getDescribe() ;
        Map<String,Schema.SObjectField> fields = r.fields.getMap() ;
        Schema.DescribeFieldResult fieldResult = fields.get(fieldName).getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        if(!ple.isEmpty()){
            for( Schema.PicklistEntry pickListVal : ple){
               // System.debug(pickListVal.getLabel() +' '+pickListVal.getValue());
               pickListValuesList.put(pickListVal.getValue(),pickListVal.getLabel());
            }    
        }
        System.debug('pickListValuesList '+pickListValuesList);
         return pickListValuesList;
    }

    public static Map<String, String> getPicklistValuesAPI(String objectName, String fieldName){
        Map<String, String> pickListValuesList= new Map<String, String>();
        System.debug('objectName : '+objectName+' fieldName : '+fieldName);
        Schema.SObjectType s = Schema.getGlobalDescribe().get(objectName) ;
        Schema.DescribeSObjectResult r = s.getDescribe() ;
        Map<String,Schema.SObjectField> fields = r.fields.getMap() ;
        Schema.DescribeFieldResult fieldResult = fields.get(fieldName).getDescribe();
        List<Schema.PicklistEntry> ple = fieldResult.getPicklistValues();
        if(!ple.isEmpty()){
            for( Schema.PicklistEntry pickListVal : ple){
               // System.debug(pickListVal.getLabel() +' '+pickListVal.getValue());
               pickListValuesList.put(pickListVal.getLabel(),pickListVal.getValue());
            }    
        }
        System.debug('pickListValuesList '+pickListValuesList);
         return pickListValuesList;
    }
}