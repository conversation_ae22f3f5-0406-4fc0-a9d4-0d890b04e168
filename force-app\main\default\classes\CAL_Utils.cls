/**
 * @description       : 
 * <AUTHOR> 🦅  | DA-technologies
 * @group             : 
 * @last modified on  : 10-07-2025
 * @last modified by  : 🦅  | DA-technologies
**/
public with sharing class CAL_Utils {

    public class CustomException extends Exception {}

		public static Departement__mdt getDepartementMap(String postalCode) {
		
		String departement = 'D';
		if (postalCode.left(2) == '97') {
			departement += postalCode.left(3);
		} else if (postalCode.left(3) == '202'
			|| postalCode.left(3) == '206') {
			
			departement += '2B';
		} else if (postalCode.left(3) == '201'
			|| postalCode.left(3) == '200') {
			
			departement += '2A';
		} else {
			departement += postalCode.left(2);
		}

		return Departement__mdt.getInstance(departement);
	}

    /**
   * <AUTHOR>  | DA-technologies 
   * @since 2025-02-25 Created
   * @description Methode pour récupération des jours franc et determiner le date travaux 
   * cas1 : Si le délai s'achève un samedi ou un dimanche, alors il est reporté au lundi. 
   * cas2: Si le délai s'achève un jour férié, alors il est reporté d'un jour. 
   * cas3:  si un délai s'achève un samedi et le lundi suivant est un jour férié, alors il est reporté au mardi.
   * @param date de travaux renseigné par l'utilisateur
   * @return 
   */
	public static Date getNextWorkingDay(Datetime dateTravaux) {
		// Récupération des BusinessHours pour "EUR"
		BusinessHours bh = [SELECT Id FROM BusinessHours WHERE Name = 'EUR' LIMIT 1];

		// Convertir la date en Datetime (début de journée)
		// Normaliser la date à minuit
		dateTravaux = DateTime.newInstance(dateTravaux.date(), Time.newInstance(0, 0, 0, 0));
	

		// Vérifier si la date est dans les heures ouvrées
		while (!BusinessHours.isWithin(bh.Id, dateTravaux)) {
			dateTravaux = dateTravaux.addDays(1); // Ajouter un jour si hors business hours
		}
		System.debug('this is date'+dateTravaux);
		// Retourner la date correspondante
		return dateTravaux.date();
	}

    	public static DateTime getNextWorkingDayBis(Datetime dateTravaux) {
		// Récupération des BusinessHours pour "EUR"
		BusinessHours bh = [SELECT Id FROM BusinessHours WHERE Name = 'EUR' LIMIT 1];

		// Convertir la date en Datetime (début de journée)
		// Normaliser la date à minuit
		dateTravaux = DateTime.newInstance(dateTravaux.date(), Time.newInstance(0, 0, 0, 0));
	

		// Vérifier si la date est dans les heures ouvrées
		while (!BusinessHours.isWithin(bh.Id, dateTravaux)) {
			dateTravaux = dateTravaux.addDays(1); // Ajouter un jour si hors business hours
		}
		System.debug('this is date'+dateTravaux);
		// Retourner la date correspondante
		return dateTravaux;
	}
}