<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 04-06-2025
  @last modified by  : <EMAIL>
-->
<aura:component implements="force:lightningQuickActionWithoutHeader,force:hasRecordId"
                access="global">
    <aura:attribute name="recordId" type="Id" />
    <aura:attribute name="errorMessage" type="String" />
    <aura:attribute name="recordTypeName" type="String" />
    <aura:attribute name="disableButton" type="Boolean" default="true" />
    <aura:attribute name="showError" type="Boolean" />
    <aura:attribute name="showWarningError" type="Boolean" />
    <aura:attribute name="showContent" type="Boolean" default="false" />
    <aura:attribute name="isProcessing" type="Boolean" default="false" />

    <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true"
        aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open slds-modal_large">
        <div class="slds-modal__container">
            <!--Modal Header-->
            <header class="slds-modal__header">
                <lightning:buttonIcon aura:id="exit" size="large" iconName="utility:close" variant="bare"
                    onclick="{!c.close}" alternativeText="Fermer la fenêtre."
                    class="slds-modal__close slds-button_icon-inverse" />
                <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">
                    <span>
                        <h4 class="title slds-text-heading--medium">Convention</h4>
                    </span>
                </h2>
            </header>
            <!--End Modal Header-->

            <!--Modal Body-->
            <div class="slds-modal__content slds-p-around_medium slds-p-top_x-small" id="modal-content-id-1">
                <div class="slds-scrollable_y" style="max-height:500px;">
                    <div class="slds-is-relative">
                        <iframe src="{! '/apex/VF03_ConventionDocs?id=' + v.recordId }" width="100%" height="735px" frameborder="0"></iframe>
                    </div>
                </div>
            </div>
            <!--End of Modal Body-->

            <!-- MODAL FOOTER -->
            <div class="modal-footer slds-modal__footer slds-size_1-of-1">
                <div class="forceChangeRecordTypeFooter">
                    <lightning:button label="Enregistrer" class="slds-button slds-button_neutral" variant="brand"
                        onclick=""  />
                    <lightning:button aura:id="Footer-id" label="Annuler" class="slds-button slds-button_neutral"
                        onclick="{!c.close}" />
                </div>
            </div>
        </div>
    </section>
</aura:component>