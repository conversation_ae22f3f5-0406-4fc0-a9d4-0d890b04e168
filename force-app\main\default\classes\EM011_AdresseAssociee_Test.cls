public class EM011_AdresseAssociee_Test {

    @TestSetup
    static void setupTestData() {
            List<Account> accounts = TestFactory.account().createList(3);
      }
    @isTest
    static void testGetAdresseAssocieByAccount() {
        Set<Id> accountIds = new Set<Id>();
        List<Account> accounts = [SELECT Id FROM Account LIMIT 3];
        for (Account account : accounts) {
            accountIds.add(account.Id);         
        }
        List<AdresseAssociee__c> adresseAssociees = [
            SELECT Id,
                   Compte__c,
                   AddresseDesTravaux__c,
                   CodePostal__c,
                   Ville__c,
                   RegionProvince__c,
                   NomDuSiteDesTravaux__c
            FROM AdresseAssociee__c 
            WHERE Compte__c IN :accountIds
        ];
        Test.startTest();
        List<AdresseAssociee__c> result = EM011_AdresseAssociee.getAdresseAssocieByAccount(accountIds);
        Test.stopTest();
        
        System.assertEquals(adresseAssociees.size(), result.size(), 'Le nombre d\'adresses associées devrait correspondre');
    }
}