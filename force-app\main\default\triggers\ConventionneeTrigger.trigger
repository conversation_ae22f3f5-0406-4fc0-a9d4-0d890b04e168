trigger ConventionneeTrigger on Contract (before insert, after insert, after update,before update) {
    if (Trigger.isAfter && Trigger.isUpdate) {
        TH001_Contract.cancelPreviousVersion(Trigger.new,Trigger.oldMap);
    }
    if (Trigger.isBefore && Trigger.isInsert) {
        TH001_Contract.handleBeforeInsert(Trigger.new);
    }
    if (Trigger.isBefore && Trigger.isUpdate) {
        TH001_Contract.handleBeforeUpdate(Trigger.new, Trigger.oldMap);
    }

    // Liste des comptes à mettre à jour
    List<Account> accountsToUpdate = new List<Account>();
    
    // Parcourir les contrats modifies
    for (Contract contract : Trigger.new) {
        if (contract.AccountId != null) {
            Account acc = new Account();
            acc.Id = contract.AccountId;
            
            // Vérifier si la convention est dans sa période active
            Date today = Date.today();
            boolean isActive = false;
            
            if (contract.StartDate != null && contract.EndDate != null) {
                isActive = (contract.StartDate <= today && contract.EndDate > today);
            }
            
            acc.Conventionn__c = isActive;
            
            accountsToUpdate.add(acc);
        }
    }
    
    // Mettre à jour des comptes
    if (!accountsToUpdate.isEmpty()) {
        update accountsToUpdate;
    }
}