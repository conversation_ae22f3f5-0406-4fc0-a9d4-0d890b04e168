/**
* @File Name : EM006_Contract
* @Description : EM de l'objet Contract
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 10, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 10, 2025|   | Initial Version
**/
public with sharing class EM006_Contract {
    /**
    * @Description      :   Cette methode permet récupérer Le contract selon la covention originale
    * @method Name      :   getContractByConventionOriginal
    * <AUTHOR>   <PERSON>ina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   originalConventionId => Id de la convention originale
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<Contract> getContractByConventionOriginal(Id originalConventionId){
          return DM006_Contract.selectContractByConventionOriginal(originalConventionId);
    }

     /**
    * @Description      :   Cette methode permet récupérer Le contract par son Id
    * @method Name      :   getContractById
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   idContract => Id du contract
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<Contract> getContractById(Id idContract){
          return DM006_Contract.selectContractById(idContract);
    }

    /**
    * @Description      :   Cette methode permet récupérer La version du convention supérieur d'une convention atuelle
    * @method Name      :   gethigherVersionofContract
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   idContract => Id du contract
    * @Param            :   currentVersion => version
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<Contract> gethigherVersionofContract(Id originalConventionId,Decimal currentVersion){
      return DM006_Contract.selecthigherVersionofContract(originalConventionId,currentVersion);
    } 
    
   
}