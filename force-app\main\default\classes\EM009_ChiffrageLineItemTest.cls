@isTest
private class EM009_ChiffrageLineItemTest {

    @isTest
    static void testGetCLIByOpportunityWithSet() {
        // Création d'une opportunité de test
        Opportunity opp = TestFactory.Opportunity().create();
        Set<Opportunity> oppSet = new Set<Opportunity>{opp};

        // Création d'un chiffrage lié à l'opportunité
        Chiffrage__c chiffrage = TestFactory.Chiffrage()
            .withOpportunity(opp.Id)
            .create();

        // Création d'une ligne de chiffrage liée au chiffrage
        ChiffrageLineItem__c lineItem = TestFactory.lineItem()
            .withChiffrage(chiffrage.Id)
            .create();

        // Appel de la méthode en passant un Set<Id> d'opportunités
        Set<Id> opportunityIds = new Set<Id>{opp.Id};
        List<ChiffrageLineItem__c> results = EM009_ChiffrageLineItem.getCLIByOpportunity(opportunityIds);

        // Vérifie que la méthode retourne bien un ou plusieurs résultats
        System.assertNotEquals(0, results.size(), 'Au moins une ligne de chiffrage est attendue');

        // Vérifie que la ligne retournée est bien celle créée
        System.assertEquals(lineItem.Id, results[0].Id);
    }

    @isTest
    static void testGetCLIByOpportunityWithSingleId() {
        // Création d'une opportunité de test
        Opportunity opp = TestFactory.Opportunity().create();

        // Création d'un chiffrage lié à l'opportunité
        Chiffrage__c chiffrage = TestFactory.Chiffrage()
            .withOpportunity(opp.Id)
            .create();

        // Création d'une ligne de chiffrage liée au chiffrage
        ChiffrageLineItem__c lineItem = TestFactory.lineItem()
            .withChiffrage(chiffrage.Id)
            .create();

        // Appel de la méthode en passant un seul Id d'opportunité
        List<ChiffrageLineItem__c> results = EM009_ChiffrageLineItem.getCLIByOpportunity(opp.Id);

        // Vérifie que la méthode retourne bien un ou plusieurs résultats
        System.assertNotEquals(0, results.size(), 'Au moins une ligne de chiffrage est attendue');

        // Vérifie que la ligne retournée est bien celle créée
        System.assertEquals(lineItem.Id, results[0].Id);
    }
}