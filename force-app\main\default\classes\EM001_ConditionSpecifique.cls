/**
* @File Name : EM001_ConditionSpecifique
* @Description : DM de l'objet ConditionSpecifique__c
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 10, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 10, 2025|   | Initial Version
**/
public with sharing class EM001_ConditionSpecifique {
    /**
    * @Description      :   Cette methode permet récupérer les conditions par conventionId
    * @method Name      :   getCSByConvention
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   conventionId => Id de convention
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> getCSByConvention(Id conventionId){
        return DM001_ConditionSpecifique.selectCSByConvention(conventionId);
    }

     /**
    * @Description      :   Cette methode permet récupérer les coditions par son ID
    * @method Name      :   getCSById
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   fostConventionId => Id du fost de convention
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static ConditionSpecifique__c getCSById(Id fostConventionId){
        return DM001_ConditionSpecifique.selectCSById(fostConventionId);

    }

     /**
    

    /**
    * @Description      :   Cette methode permet récupérer les conditions par le dossier l'opportunité et la convention
    * @method Name      :   getCSByDossierOppConvention
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   conventionId => Id de convention
    * @Param            :   dossierId => Id du dossier
    * @Param            :   opportunityId => Ie l'opportunite
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> getCSByDossierOppConvention(Id conventionId,Id dossierId,Id opportunityId){
        return DM001_ConditionSpecifique.getCSByDossierOppConvention(conventionId,dossierId,opportunityId);

    }
     /**
    * @Description      :    Cette methode permet récupérer toutes les conditions spécifiques associé à une convention(ne pas recupérer la convention atuelle)
    * @method Name      :   getAllCSOfConvention
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   parentConventionId => Id de convention
    * @Param            :   conventionId => Id de convention parent
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> getAllCSOfConvention(Id parentConventionId,Id conventionId){
        return DM001_ConditionSpecifique.selectAllCSOfConvention(parentConventionId,conventionId);
    }
       /**
    * @Description      :   Cette methode permet récupérer toutes les conditions spécifiques associé à une convention(convention actuelle inclus)
    * @method Name      :   getAllCSOfConvention_V2
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   parentConventionId => Id de convention
    * @Param            :   conventionId => Id de convention parent
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> getAllCSOfConvention_V2(Id parentConventionId,Id conventionId){
        return DM001_ConditionSpecifique.selectAllCSOfConvention_V2(parentConventionId,conventionId);
    }
}