public class TestFactory{

    public interface ITestDataBuilder {
        sObject build();
        sObject create();
        List<sObject> createList(Integer count);
    }

    public static AccountBuilder account() {
        return new AccountBuilder();
    }

    public static ContractBuilder contract() {
        return new ContractBuilder();
    }

    public static DossierBuilder dossier() {
        return new DossierBuilder();
    }

    public static OpportunityBuilder opportunity() {
        return new OpportunityBuilder();
    }

    public static ConditionSpecifiqueBuilder conditionSpecifique() {
        return new ConditionSpecifiqueBuilder();
    }

    public static UserBuilder user() {
        return new UserBuilder();
    }

    public static GrilleTarifaireBuilder grilleTarifaire() {
        return new GrilleTarifaireBuilder();
    }

    public static ChiffrageBuilder chiffrage() {
        return new ChiffrageBuilder();
    }

    public static ChiffrageLineItemBuilder lineItem() {
        return new ChiffrageLineItemBuilder();
    }

    public static FostBuilder fost() {
        return new FostBuilder();
    }    

    public static FostConventionBuilder fostConvention() {
        return new FostConventionBuilder();
    }

    public static FicheOperationBuilder ficheOperation() {
        return new FicheOperationBuilder();
    }

    public static LotEmmyBuilder lotEmmy() {
        return new LotEmmyBuilder();
    }

    public static CofracBuilder cofrac() {
        return new CofracBuilder();
    }

    public static OperationBuilder operation(){
        return new OperationBuilder();
    }

    public static OperationsCofracBuilder operationCofrac(){
        return new OperationsCofracBuilder();
    }

    public static FicheCEEBuilder ficheCEE(){
        return new FicheCEEBuilder();
    }

    public static DetailDuLotBuilder detailDuLot() {
        return new DetailDuLotBuilder();
    }

    public static AvenantBuilder avenant() {
        return new AvenantBuilder();
    }

    public static GroupBuilder group() {
        return new GroupBuilder();
    }

    public static groupMemberBuilder groupMember() {
        return new groupMemberBuilder();
    }

    public  static AdresseAssocieeBuilder adresseAssociee() {
        return new AdresseAssocieeBuilder();
    }
    public static LigneGrilleTarifiareBuilder ligneGrilleTarifaire() {
        return new LigneGrilleTarifiareBuilder();
    }
    public class AccountBuilder implements ITestDataBuilder {
        private account account;
        public AccountBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName ='Beneficiaire' LIMIT 1];
            this.account = new Account(
                RecordTypeId = rt.id,
                name= Utility.generateRandomString(10),
                SIRET__C= Utility.generateNumberString(14),
                SIREN__C= Utility.generateNumberString(14),
                Industry= 'Technology'
            );
        }
        public AccountBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName =:DeveloperName LIMIT 1];
            this.account.RecordTypeId = rt.Id;
            return this;
        }
        public AccountBuilder withName(String name) {
            this.account.name = name;
            return this;
        }
        public AccountBuilder withSiret(String siret) {
            this.account.Siret__c = siret;
            return this;
        }
        public AccountBuilder withSiren(String siren) {
            this.account.Siren__c = siren;
            return this;
        }
        public AccountBuilder withIndustry(String industry) {
            this.account.Industry = industry;
            return this;
        }
        public Account build() {
            return this.account;
        }

        public Account create() {
            insert this.account;
            return this.account;
        }

        public List<Account> createList(Integer count) {
            List<Account> accounts = new List<Account>();
            for (Integer i = 0; i < count; i++) {
                Account acc = build().clone();
                acc.Name = Utility.generateRandomString(10);
                acc.SIRET__C =  Utility.generateNumberString(14);
                acc.SIREN__C = Utility.generateNumberString(14);
                accounts.add(acc);
            }
            insert accounts;
            return accounts;
        }
    } 

    public class ContractBuilder implements ITestDataBuilder {
        private Contract contract;
        public ContractBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Contract' AND DeveloperName ='Convention' LIMIT 1];
            Account acc = TestFactory.account().create();
            this.contract = new Contract(
                RecordTypeId = rt.id,
                TypeDeCovention__c= 'MOA (Bénéficiaire)',
                AccountId= acc.Id,
                StartDate= Date.today(),
                EndDate= Date.today().addYears(1)
            );
        }
        public ContractBuilder withName(String name) {
            this.contract.Name = name;
            return this;
        }
        public ContractBuilder withConventionOriginal(Id conventionId) {
            this.contract.ConventionOriginal__c = conventionId;
            return this;
        }
        public ContractBuilder withStatus(String status) {
            this.contract.Status = status;
            return this;
        }
        public ContractBuilder withStartDate(Date startDate) {
            this.contract.StartDate = startDate;
            return this;
        }
        public ContractBuilder withEndDate(Date endDate) {
            this.contract.EndDate = endDate;
            return this;
        }
        public ContractBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Contract' AND DeveloperName =:DeveloperName LIMIT 1];
            this.contract.RecordTypeId = rt.Id;
            return this;
        }
        public ContractBuilder withType(String type) {
            this.contract.TypeDeCovention__c = type;
            return this;
        }
        public ContractBuilder withAccount(Account acc) {
            this.contract.AccountId = acc.Id;
            return this;
        }
        public ContractBuilder withVersion(Decimal version) {
            this.contract.Version__c = version;
            return this;
        }
        public Contract build() {
            return this.contract;
        }

        public Contract create() {
            insert this.contract;
            return this.contract;
        }

        public List<Contract> createList(Integer count) {
            List<Contract> contracts = new List<Contract>();
            for (Integer i = 0; i < count; i++) {
                Contract con = build().clone(false, true, false, false);
                contracts.add(con);
            }
            System.debug('Contracts to insert: ' + contracts);
            insert contracts;
            return contracts;
        }
    }

    public class DossierBuilder implements ITestDataBuilder {
        private Dossier__c dossier;

        public DossierBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Dossier__c' AND DeveloperName ='PersonnePhysique' LIMIT 1];
            List<Account> accounts = TestFactory.account().createList(4);
            this.dossier = new Dossier__c(
                RecordTypeId = rt.id,
                Beneficiaire__c=accounts[0].Id,
                MandataireApporteur__c= accounts[1].Id,
                Installateur__c= accounts[2].Id,
                BailleurSocial__c=accounts[3].Id

            );
        }
        public DossierBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Dossier__c' AND DeveloperName =:DeveloperName LIMIT 1];
            this.dossier.RecordTypeId = rt.Id;
            return this;
        }
        public Dossier__c build() {
            return this.dossier;
        }

        public Dossier__c create() {
            insert this.dossier;
            return this.dossier;
        }

        public List<Dossier__c> createList(Integer count) {
            List<Dossier__c> dossiers = new List<Dossier__c>();
            for (Integer i = 0; i < count; i++) {
                Dossier__c doss = build().clone();
                dossiers.add(doss);
            }
            insert dossiers;
            return dossiers;
        }
    }

    public class UserBuilder implements ITestDataBuilder {
        private User user;

        public UserBuilder() {
            String firstName = Utility.generateRandomString(5).toLowerCase();
            String LastName = Utility.generateRandomString(5).toLowerCase();
            Profile p = [SELECT Id FROM Profile WHERE Name = 'Administrateur système' LIMIT 1];
            this.user = new User(
                Alias = firstName,
                Username = firstName + '.' + lastName + '@example.com',
                Email = firstName + '.' + lastName + '@example.com',
                EmailEncodingKey = 'UTF-8',
                LastName = lastName,
                FirstName = firstName,
                LanguageLocaleKey = 'fr',
                LocaleSidKey = 'fr_FR',
                ProfileId = p.Id,
                TimeZoneSidKey = 'Europe/Paris',
                IsActive = false
            );
        }
        public UserBuilder WithProfile(String profileName) {
            Profile p = [SELECT Id FROM Profile WHERE Name =:profileName LIMIT 1];
            this.user.ProfileId = p.Id;
            return this;
        }
        public UserBuilder withAlias(String alias) {
            this.user.Alias = alias;
            return this;
        }
        public UserBuilder withEmail(String email) {
            this.user.Email = email;
            return this;
        }        
        public UserBuilder withFirstName(String firstName) {
            this.user.FirstName = firstName;
            return this;
        }
        public UserBuilder withLastName(String lastName) {
            this.user.LastName = lastName;
            return this;
        }
        public UserBuilder withUsername(String username) {
            this.user.Username = username;
            return this;
        }
        public UserBuilder withIsActive(Boolean IsActive){
            this.user.IsActive = IsActive;
            return this;
        }
        public UserBuilder withCountry(String country){
            this.user.Country = country;
            return this;
        }
        public User build() {
            return this.user;
        }

        public User create() {
            insert this.user;
            return this.user;
        }

        public List<User> createList(Integer count) {
            List<User> users = new List<User>();
            for (Integer i = 0; i < count; i++) {
                User usr = build().clone();
                String firstName = Utility.generateRandomString(5).toLowerCase();
                String LastName = Utility.generateRandomString(5).toLowerCase();
                usr.Alias = firstName;
                usr.Username = firstName + '.' + lastName + '@example.com';
                usr.Email = firstName + '.' + lastName + '@example.com';
                usr.LastName = lastName;
                usr.FirstName = firstName;
                users.add(usr);
            }
            insert users;
            return users;
        }
    }
    
    public class OpportunityBuilder implements ITestDataBuilder {
        private Opportunity opportunity;

        public OpportunityBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName ='AE' LIMIT 1];
            this.opportunity = new Opportunity(
                RecordTypeId = rt.id,
                accountId = TestFactory.account().create().Id,
                Name = 'Test Opportunity',
                StageName = 'Prospecting',
                CloseDate = Date.today().addMonths(1),
                date_pr_visionnel_de_signature_devis__c = Date.today()
            );
        }
     

        public OpportunityBuilder withName(String name) {
            this.opportunity.Name = name;
            return this;
        }

        public OpportunityBuilder withStage(String stage) {
            this.opportunity.StageName = stage;
            return this;
        }

        public OpportunityBuilder withCloseDate(Date closeDate) {
            this.opportunity.CloseDate = closeDate;
            return this;
        }

        public OpportunityBuilder withAccount(String accId) {
            this.opportunity.AccountId = accId;
            return this;
        }

        public OpportunityBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName =:DeveloperName LIMIT 1];
            this.opportunity.RecordTypeId = rt.Id;
            return this;
        }

        public Opportunity build() {
            return this.opportunity;
        }

        public Opportunity create() {
            insert this.opportunity;
            return this.opportunity;
        }

        public List<Opportunity> createList(Integer count) {
            List<Opportunity> opportunities = new List<Opportunity>();
            for (Integer i = 0; i < count; i++) {
                Opportunity opp = build().clone();
                opp.Name += ' ' + (i + 1);
                opportunities.add(opp);
            }
            insert opportunities;
            return opportunities;
        }
    }

    public class ConditionSpecifiqueBuilder implements ITestDataBuilder {
        private ConditionSpecifique__c conditionSpecifique;
        public ConditionSpecifiqueBuilder() {
            this.conditionSpecifique = new ConditionSpecifique__c ();
            this.conditionSpecifique.Convention__c = TestFactory.contract().create().Id;
            this.conditionSpecifique.Dossier__c = TestFactory.dossier().create().Id;
            // Opportunity opp = TestFactory.opportunity().withAccount(acc.Id).create();
        }
        public ConditionSpecifiqueBuilder withConvention(Id contractId) {
            this.conditionSpecifique.Convention__c = contractId;
            return this;
        }
        public ConditionSpecifiqueBuilder withDossier(Id dossierId) {
            this.conditionSpecifique.Dossier__c = dossierId;
            return this;
        }
        // public ConditionSpecifiqueBuilder withOpportunity(Id opportunityId) {
        //     this.conditionSpecifique.Opportunite__c = opportunityId;
        //     return this;
        // }
        public ConditionSpecifique__c build() {
            return this.conditionSpecifique;
        }

        public ConditionSpecifique__c create() {
            insert this.conditionSpecifique;
            return this.conditionSpecifique;
        }

        public List<ConditionSpecifique__c> createList(Integer count) {
            List<ConditionSpecifique__c> conditions = new List<ConditionSpecifique__c>();
            for (Integer i = 0; i < count; i++) {
                ConditionSpecifique__c cs = build().clone();
                conditions.add(cs);
            }
            insert conditions;
            return conditions;
        }
    }

    public class GrilleTarifaireBuilder implements ITestDataBuilder {
        private GrilleTarifaire__c grilleTarifaire;
        public GrilleTarifaireBuilder() {
            this.grilleTarifaire = new GrilleTarifaire__c ();
            this.grilleTarifaire.Convention__c = TestFactory.contract().create().Id;

        }
        public GrilleTarifaireBuilder withConvention(Id contractId) {
            this.grilleTarifaire.Convention__c = contractId;
            return this;
        }

        public GrilleTarifaire__c build() {
            return this.grilleTarifaire;
        }

        public GrilleTarifaire__c create() {
            insert this.grilleTarifaire;
            return this.grilleTarifaire;
        }

        public List<GrilleTarifaire__c> createList(Integer count) {
            List<GrilleTarifaire__c> grilleTarifaires = new List<GrilleTarifaire__c>();
            for (Integer i = 0; i < count; i++) {
                GrilleTarifaire__c gt = build().clone();
                grilleTarifaires.add(gt);
            }
            insert grilleTarifaires;
            return grilleTarifaires;
        }
    }

    public class ChiffrageLineItemBuilder implements ITestDataBuilder {
        private ChiffrageLineItem__c lineItem;
        public ChiffrageLineItemBuilder() {
            this.lineItem = new ChiffrageLineItem__c();
            this.lineItem.Chiffrage__c = TestFactory.Chiffrage().create().Id;
            
        }

        public ChiffrageLineItemBuilder withChiffrage(Id chiffrageId) {
            this.lineItem.Chiffrage__c = chiffrageId;
            return this;
        }

        public ChiffrageLineItem__c build() {
            return this.lineItem;
        }

        public ChiffrageLineItem__c create() {
            insert this.lineItem;
            return this.lineItem;
        }

        public List<ChiffrageLineItem__c> createList(Integer count) {
            List<ChiffrageLineItem__c> lineItems = new List<ChiffrageLineItem__c>();
            for (Integer i = 0; i < count; i++) {
                ChiffrageLineItem__c li = build().clone();
                lineItems.add(li);
            }
            insert lineItems;
            return lineItems;
        }
        public List<ChiffrageLineItem__c> createList(set<Id> chiffrageIds) {
            List<ChiffrageLineItem__c> lineItems = new List<ChiffrageLineItem__c>();
            for (Id chiffrageId : chiffrageIds) {
                ChiffrageLineItem__c li = build().clone();
                li.Chiffrage__c = chiffrageId;
                lineItems.add(li);
            }
            insert lineItems;
            return lineItems;
        }
    }

    public class ChiffrageBuilder implements ITestDataBuilder {
        private Chiffrage__c Chiffrage;
        public ChiffrageBuilder() {
            this.Chiffrage = new Chiffrage__c ();
            this.Chiffrage.Opportunity__c = TestFactory.Opportunity().create().Id;

        }

        public ChiffrageBuilder withOpportunity(Id OpportunityId) {
            this.Chiffrage.Opportunity__c = OpportunityId;
            return this;
        }


        public Chiffrage__c build() {
            return this.Chiffrage;
        }

        public Chiffrage__c create() {
            insert this.Chiffrage;
            return this.Chiffrage;
        }

        public List<Chiffrage__c> createList(Integer count) {
            List<Chiffrage__c> Chiffrages = new List<Chiffrage__c>();
            for (Integer i = 0; i < count; i++) {
                Chiffrage__c ch = build().clone();
                Chiffrages.add(ch);
            }
            insert Chiffrages;
            return Chiffrages;
        }
    }

    public class FostBuilder implements ITestDataBuilder {
        private Product2 fost;

        public FostBuilder() {
            this.fost = new Product2();
            this.fost.Name = 'Fost ' + Utility.generateRandomString(5);
            this.fost.ProductCode = 'Fost ' + Utility.generateRandomString(5);
          
        }
        

        public FostBuilder withName(String name) {
            this.fost.Name = name;
            return this;
        }

        public FostBuilder withProductCode(String productCode) {
            this.fost.ProductCode = productCode;
            return this;
        }

        public Product2 build() {
            return this.fost;
        }

        public Product2 create() {
            insert this.fost;
            return this.fost;
        }

        public List<Product2> createList(Integer count) {
            List<Product2> fests = new List<Product2>();
            for (Integer i = 0; i < count; i++) {
                Product2 fest = build().clone();
                fest.Name += ' ' + (i + 1);
                fest.ProductCode += ' ' + (i + 1);
                fests.add(fest);
            }
            insert fests;
            return fests;
        }
    }

    public class FostConventionBuilder implements ITestDataBuilder {
        private FOST_de_convention__c fostConvention;

        public FostConventionBuilder() {
            this.fostConvention = new FOST_de_convention__c();
            this.fostConvention.Fost__c = TestFactory.fost().create().Id;
            this.fostConvention.Convention__c = TestFactory.contract().create().Id;
        }

        public FostConventionBuilder withFost(Id fostId) {
            this.fostConvention.Fost__c = fostId;
            return this;
        }

        public FostConventionBuilder withConvention(Id conventionId) {
            this.fostConvention.Convention__c = conventionId;
            return this;
        }

        public FOST_de_convention__c build() {
            return this.fostConvention;
        }

        public FOST_de_convention__c create() {
            insert this.fostConvention;
            return this.fostConvention;
        }

        public List<FOST_de_convention__c> createList(Integer count) {
            List<FOST_de_convention__c> fostConventions = new List<FOST_de_convention__c>();
            for (Integer i = 0; i < count; i++) {
                FOST_de_convention__c fc = build().clone();
                fostConventions.add(fc);
            }
            insert fostConventions;
            return fostConventions;
        }
    }

    public class FicheOperationBuilder implements ITestDataBuilder {
        private FicheOperation__c ficheOperation;

        public FicheOperationBuilder() {
            this.ficheOperation = new FicheOperation__c();
            this.ficheOperation.Name = 'Fiche Operation ' + Utility.generateRandomString(5);
            this.ficheOperation.Offre__c = 'CDP'; // Default offer typ
            this.ficheOperation.Secteur__c = 'Secteur A'; // Default sector
            this.ficheOperation.ZoneApplication__c = 'Zone A'; // Default application zone
            this.ficheOperation.isSoumisRGE__c = true; // Default RGE status
          
        }

        public FicheOperationBuilder withName(String name) {
            this.ficheOperation.Name = name;
            return this;
        }

        public FicheOperationBuilder withOffre(String offre) {
            this.ficheOperation.offre__c = offre;
            return this;
        }
        // public FicheOperationBuilder withDateEngagement(Date dateEngagement) {
        //     this.ficheOperation.dateEngagement__c = dateEngagement;
        //     return this;
        // }
        public FicheOperationBuilder withSecteur(String secteur) {
            this.ficheOperation.secteur__c = secteur;
            return this;
        }
        public FicheOperationBuilder withZoneApplication(String zoneApplication) {
            this.ficheOperation.zoneApplication__c = zoneApplication;
            return this;
        }
        public FicheOperationBuilder withIsRGE(Boolean isRGE) {
            this.ficheOperation.isSoumisRGE__c = isRGE;
            return this;
        }

        public FicheOperation__c build() {
            return this.ficheOperation;
        }

        public FicheOperation__c create() {
            insert this.ficheOperation;
            return this.ficheOperation;
        }

        public List<FicheOperation__c> createList(Integer count) {
            List<FicheOperation__c> ficheOperations = new List<FicheOperation__c>();
            for (Integer i = 0; i < count; i++) {
                FicheOperation__c fo = build().clone();
                fo.Name += ' ' + (i + 1);
                fo.offre__c += ' ' + (i + 1);
                // fo.dateEngagement__c = Date.today().addDays(i); // Different engagement dates
                fo.secteur__c += ' ' + (i + 1);
                fo.zoneApplication__c += ' ' + (i + 1);
                
                // Add any other fields that need to be varied  
                ficheOperations.add(fo);
            }
            insert ficheOperations;
            return ficheOperations;
        }
    }

    public class LotEmmyBuilder implements ITestDataBuilder {
        private Lot__c lot;

        public LotEmmyBuilder() {
            this.lot = new Lot__c();
            this.lot.Name = Utility.generateNumberString(5);
        }

        public LotEmmyBuilder withName(String name) {
            this.lot.Name = name;
            return this;
        }

        public Lot__c build() {
            return this.lot;
        }

        public Lot__c create() {
            insert this.lot;
            return this.lot;
        }

        public List<Lot__c> createList(Integer count) {
            List<Lot__c> lots = new List<Lot__c>();
            for (Integer i = 0; i < count; i++) {
                Lot__c lotClone = build().clone();
                lotClone.Name += ' ' + (i + 1);
                lots.add(lotClone);
            }
            insert lots;
            return lots;
        }
    }
    
    public class CofracBuilder implements ITestDataBuilder {
        private COFRAC__c cofrac;

        public CofracBuilder() {
            this.cofrac = new Cofrac__c();
            this.cofrac.Name = Utility.generateNumberString(5);
        }   

        public CofracBuilder withName(String name) {
            this.cofrac.Name = name;
            return this;
        }

        public Cofrac__c build() {
            return this.cofrac;
        }

        public Cofrac__c create() {
            insert this.cofrac;
            return this.cofrac;
        }

        public List<Cofrac__c> createList(Integer count) {
            List<Cofrac__c> cofracs = new List<Cofrac__c>();
            for (Integer i = 0; i < count; i++) {
                Cofrac__c cofracClone = build().clone();
                cofracClone.Name += ' ' + (i + 1);
                cofracs.add(cofracClone);
            }
            insert cofracs;
            return cofracs;
        }
    } 

    public class FicheCEEBuilder implements ITestDataBuilder {
        private FicheCEE__c ficheCEE;

        public FicheCEEBuilder() {
            this.ficheCEE = new FicheCEE__c();
            this.ficheCEE.Name = Utility.generateNumberString(5);
            this.ficheCEE.Actif__c= true;
        }
        
        public FicheCEEBuilder withName(String name){
            this.ficheCEE.Name = name;
            return this;
        }      

        public FicheCEE__c build() {
            return this.ficheCEE;
        }

        public FicheCEE__c create() {
            insert this.ficheCEE;
            return this.ficheCEE;
        }

        public List<FicheCEE__c> createList(Integer count) {
            List<FicheCEE__c> ficheCEE = new List<FicheCEE__c>();
            for (Integer i = 0; i < count; i++) {
                FicheCEE__c fc = build().clone();
                ficheCEE.add(fc);
            }
            insert ficheCEE;
            return ficheCEE;
        }
    }

    public class OperationBuilder implements ITestDataBuilder {
        private Operation__c operation;

        public OperationBuilder() {
            this.operation = new Operation__c();
            this.operation.Dossier__c = TestFactory.dossier().create().Id;
            this.operation.FicheCEE__c = TestFactory.ficheCEE().create().Id;
        }

        public OperationBuilder withDossier(Id dossierId) {
            this.operation.Dossier__c = dossierId;
            return this;
        }

        public OperationBuilder withFicheCEE(Id ficheCEEId) {
            this.operation.FicheCEE__c = ficheCEEId;
            return this;
        }

        public Operation__c build() {
            return this.operation;
        }

        public Operation__c create() {
            insert this.operation;
            return this.operation;
        }
        
        public List<Operation__c> createList(Integer count) {
            List<Operation__c> operations = new List<Operation__c>();

            for (Integer i = 0; i < count; i++) {
                Operation__c opeClone = build().clone();
                operations.add(opeClone);
            }
            insert operations;
            return operations;
        }
    }    

    public class OperationsCofracBuilder implements ITestDataBuilder {
        private operationsCofrac__c operationCofrac;

        public OperationsCofracBuilder() {
            this.operationCofrac = new operationsCofrac__c();
            this.operationCofrac.Name = Utility.generateNumberString(5);
            this.operationCofrac.Cofrac__c = TestFactory.cofrac().create().Id;
            this.operationCofrac.Operation__c = TestFactory.operation().create().Id;
        }

        public operationsCofrac__c build() {
            return this.operationCofrac;
        }

        public operationsCofrac__c create() {
            insert this.operationCofrac;
            return this.operationCofrac;
        }

        public List<operationsCofrac__c> createList(Integer count) {
            List<operationsCofrac__c> operationsCofracs = new List<operationsCofrac__c>();
            for (Integer i = 0; i < count; i++) {
                operationsCofrac__c opeCofracClone = build().clone();
                 opeCofracClone.Name += ' ' + (i + 1);
                 operationsCofracs.add(opeCofracClone);
            }
            insert operationsCofracs;
            return operationsCofracs;
        }
    }

    public class DetailDuLotBuilder implements ITestDataBuilder {
        private DetailDuLot__c detailDuLot;

        public DetailDuLotBuilder() {
            Dossier__c dossier = TestFactory.dossier().create();
            Operation__c operation = TestFactory.operation().withDossier(dossier.Id).create();

            this.detailDuLot = new DetailDuLot__c();
            this.detailDuLot.Lot__c = TestFactory.lotEmmy().create().Id;
            this.detailDuLot.Dossier__c = dossier.Id;
            this.detailDuLot.OperationCEEDuDossier__c = operation.Id; // Assuming this is a field in DetailDuLot__c
        }

        public DetailDuLotBuilder withLot(Id lotId) {
            this.detailDuLot.Lot__c = lotId;
            return this;
        }

        public DetailDuLotBuilder withDossier(Id dossierId) {
            this.detailDuLot.Dossier__c = dossierId;
            return this;
        }

        public DetailDuLotBuilder withOperationCEEDuDossier(Id operationId) {
            this.detailDuLot.OperationCEEDuDossier__c = operationId;
            return this;
        }

        public DetailDuLot__c build() {
            return this.detailDuLot;
        }

        public DetailDuLot__c create() {
            insert this.detailDuLot;
            return this.detailDuLot;
        }

        public List<DetailDuLot__c> createList(Integer count) {
            List<DetailDuLot__c> detailDuLots = new List<DetailDuLot__c>();
            for (Integer i = 0; i < count; i++) {
                DetailDuLot__c detailClone = build().clone();
                detailDuLots.add(detailClone);
            }
            insert detailDuLots;
            return detailDuLots;
        }
    }

    public class AvenantBuilder implements ITestDataBuilder {
        private Avenant__c avenant;

        public AvenantBuilder() {
            this.avenant = new Avenant__c();
            this.avenant.Name = Utility.generateNumberString(5);
            this.avenant.Convention__c = TestFactory.contract().create().Id;
        }

        public AvenantBuilder withName(String name) {
            this.avenant.Name = name;
            return this;
        }

        public AvenantBuilder withConvention(Id conventionId) {
            this.avenant.Convention__c = conventionId;
            return this;
        }

        public Avenant__c build() {
            return this.avenant;
        }

        public Avenant__c create() {
            insert this.avenant;
            return this.avenant;
        }

        public List<Avenant__c> createList(Integer count) {
            List<Avenant__c> avenants = new List<Avenant__c>();
            for (Integer i = 0; i < count; i++) {
                Avenant__c avenClone = build().clone();
                avenants.add(avenClone);
            }
            insert avenants;
            return avenants;
        }
    }

    public class GroupBuilder implements ITestDataBuilder {
        private Group mygroup;

        public GroupBuilder() {
            this.mygroup = new Group();
            this.mygroup.Name = 'Test Group ' + Utility.generateRandomString(5);
            this.mygroup.Type = 'Regular'; // Default type
        }

        public GroupBuilder withName(String name) {
            this.mygroup.Name = name;
            return this;
        }

        public GroupBuilder withType(String type) {
            this.mygroup.Type = type;
            return this;
        }

        public GroupBuilder withDeveloperName(String devName) {
            this.mygroup.DeveloperName = devName;
            return this;
        }

        public Group build() {
            return this.mygroup;
        }

        public Group create() {
            insert this.mygroup;
            return this.mygroup;
        }

        public List<Group> createList(Integer count) {
            List<Group> groups = new List<Group>();
            for (Integer i = 0; i < count; i++) {
                Group grp = build().clone();
                grp.Name += ' ' + (i + 1);
                groups.add(grp);
            }
            insert groups;
            return groups;
        }
    }

    public class GroupMemberBuilder implements ITestDataBuilder {
        private GroupMember groupMember;

        public GroupMemberBuilder() {
            this.groupMember = new GroupMember();
            this.groupMember.GroupId = TestFactory.group().create().Id;
            this.groupMember.UserOrGroupId = TestFactory.user().create().Id;
        }

        public GroupMemberBuilder withGroupId(Id groupId) {
            this.groupMember.GroupId = groupId;
            return this;
        }

        public GroupMemberBuilder withUserOrGroupId(Id userOrGroupId) {
            this.groupMember.UserOrGroupId = userOrGroupId;
            return this;
        }

        public GroupMember build() {
            return this.groupMember;
        }

        public GroupMember create() {
            insert this.groupMember;
            return this.groupMember;
        }
        
        public List<GroupMember> createList(Integer count) {
            List<GroupMember> groupMembers = new List<GroupMember>();
            List<User> users = TestFactory.user().createList(count);
            for (user u : users) {
                GroupMember gm = build().clone();
                gm.UserOrGroupId = u.Id;
                groupMembers.add(gm);
            }
            insert groupMembers;
            return groupMembers;
        }
        public List<GroupMember> createList(Set<Id> usersIds,Id groupId) {
            List<GroupMember> groupMembers = new List<GroupMember>();
            for (Id userId : usersIds) {
                GroupMember gm = build().clone();
                gm.UserOrGroupId = userId;
                gm.GroupId = groupId;
                groupMembers.add(gm);
            }
            insert groupMembers;
            return groupMembers;
        }
    }

    public class AdresseAssocieeBuilder implements ITestDataBuilder {
        private AdresseAssociee__c adresseAssociee;
        public AdresseAssocieeBuilder() {
            this.adresseAssociee = new AdresseAssociee__c();
            this.adresseAssociee.Compte__c = TestFactory.account().create().Id;
            this.adresseAssociee.AddresseDesTravaux__c = Utility.generateRandomString(5);
            this.adresseAssociee.CodePostal__c = Utility.generateNumberString(5);
            this.adresseAssociee.Ville__c = Utility.generateRandomString(5);
            this.adresseAssociee.RegionProvince__c = Utility.generateRandomString(5);
            this.adresseAssociee.NomDuSiteDesTravaux__c = Utility.generateRandomString(5);
        }
        public AdresseAssocieeBuilder withBeneficiaire(Id beneficiaireId) {
            this.adresseAssociee.Compte__c = beneficiaireId;
            return this;
        }
        public AdresseAssocieeBuilder withAddresseDesTravaux(String addresseDesTravaux) {
            this.adresseAssociee.AddresseDesTravaux__c = addresseDesTravaux;
            return this;
        }
        public AdresseAssocieeBuilder withCodePostal(String codePostal) {
            this.adresseAssociee.CodePostal__c = codePostal;
            return this;
        }
        public AdresseAssocieeBuilder withVille(String ville) {
            this.adresseAssociee.Ville__c = ville;
            return this;
        }
        public AdresseAssocieeBuilder withRegionProvince(String regionProvince) {
            this.adresseAssociee.RegionProvince__c = regionProvince;
            return this;
        }
        public AdresseAssocieeBuilder withNomDuSiteDesTravaux(String nomDuSiteDesTravaux) {
            this.adresseAssociee.NomDuSiteDesTravaux__c = nomDuSiteDesTravaux;
            return this;
        }
        public AdresseAssociee__c build() {
            return this.adresseAssociee;
        }
        public AdresseAssociee__c create() {
            insert this.adresseAssociee;
            return this.adresseAssociee;
        }
        public List<AdresseAssociee__c> createList(Integer count) {
            List<AdresseAssociee__c> adresseAssociees = new List<AdresseAssociee__c>();
            for (Integer i = 0; i < count; i++) {   
                AdresseAssociee__c adresseAssocieeClone = build().clone();  
                adresseAssociees.add(adresseAssocieeClone);
            }
            insert adresseAssociees;
            return adresseAssociees;
        }
    }
    public class LigneGrilleTarifiareBuilder implements ITestDataBuilder {
        private LigneGrilleTarifiare__c ligneGrilleTarifiare;
        public LigneGrilleTarifiareBuilder() {
            this.ligneGrilleTarifiare = new LigneGrilleTarifiare__c();
            this.ligneGrilleTarifiare.GrilleTarifaire__c = TestFactory.grilleTarifaire().create().Id;
            this.ligneGrilleTarifiare.Name = Utility.generateRandomString(5);
        }
        public LigneGrilleTarifiareBuilder withGrilleTarifaire(Id grilleTarifaireId) {
            this.ligneGrilleTarifiare.GrilleTarifaire__c = grilleTarifaireId;
            return this;
        }
        public LigneGrilleTarifiareBuilder withName(String name) {
            this.ligneGrilleTarifiare.Name = name;
            return this;
        }
        public LigneGrilleTarifiare__c build() {
            return this.ligneGrilleTarifiare;
        }
        public LigneGrilleTarifiare__c create() {
            insert this.ligneGrilleTarifiare;
            return this.ligneGrilleTarifiare;
        }
        public List<LigneGrilleTarifiare__c> createList(Integer count) {
            List<LigneGrilleTarifiare__c> lignes = new List<LigneGrilleTarifiare__c>();
            for (Integer i = 0; i < count; i++) {
                LigneGrilleTarifiare__c lg = build().clone();
              
                lignes.add(lg);
            }
            insert lignes;
            return lignes;
        }
}}