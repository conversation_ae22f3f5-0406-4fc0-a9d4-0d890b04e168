public class TestFactory{

    public interface ITestDataBuilder {
        sObject build();
        sObject create();
        List<sObject> createList(Integer count);
    }

    public static AccountBuilder account() {
        return new AccountBuilder();
    }

    public static ContractBuilder contract() {
        return new ContractBuilder();
    }

    public static DossierBuilder dossier() {
        return new DossierBuilder();
    }

    public static OpportunityBuilder opportunity() {
        return new OpportunityBuilder();
    }

    public static ConditionSpecifiqueBuilder conditionSpecifique() {
        return new ConditionSpecifiqueBuilder();
    }

    public static UserBuilder user() {
        return new UserBuilder();
    }

    public static GrilleTarifaireBuilder grilleTarifaire() {
        return new GrilleTarifaireBuilder();
    }

    public static ChiffrageBuilder chiffrage() {
        return new ChiffrageBuilder();
    }

    public static FostBuilder fost() {
        return new FostBuilder();
    }
    

    public static FostConventionBuilder fostConvention() {
        return new FostConventionBuilder();
    }

    public static BuilderFicheOperation ficheOperation() {
        return new BuilderFicheOperation();
    }

    public static LotEmmyBuilder lotEmmy() {
        return new LotEmmyBuilder();
    }

    public static CofracBuilder cofrac() {
        return new CofracBuilder();
    }

    public static OperationBuilder operation(){
        return new OperationBuilder();
    }

    public static OperationsCofracBuilder operationCofrac(){
        return new OperationsCofracBuilder();
    }

    public class AccountBuilder implements ITestDataBuilder {
        private account account;
        public AccountBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName ='Beneficiaire' LIMIT 1];
            this.account = new Account(
                RecordTypeId = rt.id,
                name= Utility.generateRandomString(10),
                SIRET__C= Utility.generateNumberString(14),
                SIREN__C= Utility.generateNumberString(14),
                Industry= 'Technology'
            );
        }
        public AccountBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName =:DeveloperName LIMIT 1];
            this.account.RecordTypeId = rt.Id;
            return this;
        }
        public AccountBuilder withName(String name) {
            this.account.name = name;
            return this;
        }
        public AccountBuilder withSiret(String siret) {
            this.account.Siret__c = siret;
            return this;
        }
        public AccountBuilder withSiren(String siren) {
            this.account.Siren__c = siren;
            return this;
        }
        public AccountBuilder withIndustry(String industry) {
            this.account.Industry = industry;
            return this;
        }
        public Account build() {
            return this.account;
        }

        public Account create() {
            insert this.account;
            return this.account;
        }

        public List<Account> createList(Integer count) {
            List<Account> accounts = new List<Account>();
            for (Integer i = 0; i < count; i++) {
                Account acc = build().clone();
                acc.Name = Utility.generateRandomString(10);
                acc.SIRET__C =  Utility.generateNumberString(14);
                acc.SIREN__C = Utility.generateNumberString(14);
                accounts.add(acc);
            }
            insert accounts;
            return accounts;
        }
    } 

    public class ContractBuilder implements ITestDataBuilder {
        private Contract contract;
        public ContractBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Contract' AND DeveloperName ='Convention' LIMIT 1];
            Account acc = TestFactory.account().create();
            this.contract = new Contract(
                RecordTypeId = rt.id,
                TypeDeCovention__c= 'MOA (Bénéficiaire)',
                AccountId= acc.Id,
                StartDate= Date.today(),
                EndDate= Date.today().addYears(1)
            );
        }
        public ContractBuilder withName(String name) {
            this.contract.Name = name;
            return this;
        }
        public ContractBuilder withConventionOriginal(Id conventionId) {
            this.contract.ConventionOriginal__c = conventionId;
            return this;
        }
        public ContractBuilder withStatus(String status) {
            this.contract.Status = status;
            return this;
        }
        public ContractBuilder withStartDate(Date startDate) {
            this.contract.StartDate = startDate;
            return this;
        }
        public ContractBuilder withEndDate(Date endDate) {
            this.contract.EndDate = endDate;
            return this;
        }
        public ContractBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Contract' AND DeveloperName =:DeveloperName LIMIT 1];
            this.contract.RecordTypeId = rt.Id;
            return this;
        }
        public ContractBuilder withType(String type) {
            this.contract.TypeDeCovention__c = type;
            return this;
        }
        public ContractBuilder withAccount(Account acc) {
            this.contract.AccountId = acc.Id;
            return this;
        }
        public ContractBuilder withVersion(Decimal version) {
            this.contract.Version__c = version;
            return this;
        }
        public Contract build() {
            return this.contract;
        }

        public Contract create() {
            insert this.contract;
            return this.contract;
        }

        public List<Contract> createList(Integer count) {
            List<Contract> contracts = new List<Contract>();
            for (Integer i = 0; i < count; i++) {
                Contract con = build().clone(false, true, false, false);
                contracts.add(con);
            }
            System.debug('Contracts to insert: ' + contracts);
            insert contracts;
            return contracts;
        }
    }

    public class DossierBuilder implements ITestDataBuilder {
        private Dossier__c dossier;

        public DossierBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Dossier__c' AND DeveloperName ='PersonnePhysique' LIMIT 1];
            List<Account> accounts = TestFactory.account().createList(4);
            this.dossier = new Dossier__c(
                RecordTypeId = rt.id,
                Beneficiaire__c=accounts[0].Id,
                MandataireApporteur__c= accounts[1].Id,
                Installateur__c= accounts[2].Id,
                BailleurSocial__c=accounts[3].Id

            );
        }
        public DossierBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Dossier__c' AND DeveloperName =:DeveloperName LIMIT 1];
            this.dossier.RecordTypeId = rt.Id;
            return this;
        }
        public Dossier__c build() {
            return this.dossier;
        }

        public Dossier__c create() {
            insert this.dossier;
            return this.dossier;
        }

        public List<Dossier__c> createList(Integer count) {
            List<Dossier__c> dossiers = new List<Dossier__c>();
            for (Integer i = 0; i < count; i++) {
                Dossier__c doss = build().clone();
                dossiers.add(doss);
            }
            insert dossiers;
            return dossiers;
        }
    }

    public class UserBuilder implements ITestDataBuilder {
        private User user;

        public UserBuilder() {
                Profile p = [SELECT Id FROM Profile WHERE Name = 'Administrateur système' LIMIT 1];
            User adminProfile  = new User(
                Alias = 'adminu',
                Email = '<EMAIL>',
                EmailEncodingKey = 'UTF-8',
                LastName = 'TestUser',
                LanguageLocaleKey = 'fr',
                LocaleSidKey = 'fr_FR',
                ProfileId = p.Id,
                TimeZoneSidKey = 'Europe/Paris',
                    Username = 'testuser' + DateTime.now().getTime() + '@example.com'
            );
        }
        public UserBuilder WithProfile(String profileName) {
            Profile p = [SELECT Id FROM Profile WHERE Name =:profileName LIMIT 1];
            this.user.ProfileId = p.Id;
            return this;
        }
        public UserBuilder withAlias(String alias) {
            this.user.Alias = alias;
            return this;
        }
        public UserBuilder withEmail(String email) {
            this.user.Email = email;
            return this;
        }
        public UserBuilder withLastName(String lastName) {
            this.user.LastName = lastName;
            return this;
        }
        public UserBuilder withUsername(String username) {
            this.user.Username = username;
            return this;
        }
        public User build() {
            return this.user;
        }

        public User create() {
            insert this.user;
            return this.user;
        }

        public List<User> createList(Integer count) {
            List<User> users = new List<User>();
            for (Integer i = 0; i < count; i++) {
                User usr = build().clone();
                usr.Username += ' ' + (i + 1);
                users.add(usr);
            }
            insert users;
            return users;
        }
    }
    
    public class OpportunityBuilder implements ITestDataBuilder {
        private Opportunity opportunity;

        public OpportunityBuilder() {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName ='AE' LIMIT 1];
            this.opportunity = new Opportunity(
                RecordTypeId = rt.id,
                accountId = TestFactory.account().create().Id,
                Name = 'Test Opportunity',
                StageName = 'Prospecting',
                CloseDate = Date.today().addMonths(1),
                date_pr_visionnel_de_signature_devis__c = Date.today()
            );
        }
     

        public OpportunityBuilder withName(String name) {
            this.opportunity.Name = name;
            return this;
        }

        public OpportunityBuilder withStage(String stage) {
            this.opportunity.StageName = stage;
            return this;
        }

        public OpportunityBuilder withCloseDate(Date closeDate) {
            this.opportunity.CloseDate = closeDate;
            return this;
        }

        public OpportunityBuilder withAccount(String accId) {
            this.opportunity.AccountId = accId;
            return this;
        }

        public OpportunityBuilder withRecordType(String DeveloperName) {
            RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName =:DeveloperName LIMIT 1];
            this.opportunity.RecordTypeId = rt.Id;
            return this;
        }

        public Opportunity build() {
            return this.opportunity;
        }

        public Opportunity create() {
            insert this.opportunity;
            return this.opportunity;
        }

        public List<Opportunity> createList(Integer count) {
            List<Opportunity> opportunities = new List<Opportunity>();
            for (Integer i = 0; i < count; i++) {
                Opportunity opp = build().clone();
                opp.Name += ' ' + (i + 1);
                opportunities.add(opp);
            }
            insert opportunities;
            return opportunities;
        }
    }

    public class ConditionSpecifiqueBuilder implements ITestDataBuilder {
        private ConditionSpecifique__c conditionSpecifique;
        public ConditionSpecifiqueBuilder() {
            this.conditionSpecifique = new ConditionSpecifique__c ();
            this.conditionSpecifique.Convention__c = TestFactory.contract().create().Id;
            this.conditionSpecifique.Dossier__c = TestFactory.dossier().create().Id;
            // Opportunity opp = TestFactory.opportunity().withAccount(acc.Id).create();
        }
        public ConditionSpecifiqueBuilder withConvention(Id contractId) {
            this.conditionSpecifique.Convention__c = contractId;
            return this;
        }
        public ConditionSpecifiqueBuilder withDossier(Id dossierId) {
            this.conditionSpecifique.Dossier__c = dossierId;
            return this;
        }
        // public ConditionSpecifiqueBuilder withOpportunity(Id opportunityId) {
        //     this.conditionSpecifique.Opportunite__c = opportunityId;
        //     return this;
        // }
        public ConditionSpecifique__c build() {
            return this.conditionSpecifique;
        }

        public ConditionSpecifique__c create() {
            insert this.conditionSpecifique;
            return this.conditionSpecifique;
        }

        public List<ConditionSpecifique__c> createList(Integer count) {
            List<ConditionSpecifique__c> conditions = new List<ConditionSpecifique__c>();
            for (Integer i = 0; i < count; i++) {
                ConditionSpecifique__c cs = build().clone();
                conditions.add(cs);
            }
            insert conditions;
            return conditions;
        }
    }

    public class GrilleTarifaireBuilder implements ITestDataBuilder {
        private GrilleTarifaire__c grilleTarifaire;
        public GrilleTarifaireBuilder() {
            this.grilleTarifaire = new GrilleTarifaire__c ();
            this.grilleTarifaire.Convention__c = TestFactory.contract().create().Id;

        }
        public GrilleTarifaireBuilder withConvention(Id contractId) {
            this.grilleTarifaire.Convention__c = contractId;
            return this;
        }

        public GrilleTarifaire__c build() {
            return this.grilleTarifaire;
        }

        public GrilleTarifaire__c create() {
            insert this.grilleTarifaire;
            return this.grilleTarifaire;
        }

        public List<GrilleTarifaire__c> createList(Integer count) {
            List<GrilleTarifaire__c> grilleTarifaires = new List<GrilleTarifaire__c>();
            for (Integer i = 0; i < count; i++) {
                GrilleTarifaire__c gt = build().clone();
                grilleTarifaires.add(gt);
            }
            insert grilleTarifaires;
            return grilleTarifaires;
        }
    }

    public class ChiffrageBuilder implements ITestDataBuilder {
        private Chiffrage__c Chiffrage;
        public ChiffrageBuilder() {
            this.Chiffrage = new Chiffrage__c ();
             this.Chiffrage.Opportunity__c = TestFactory.Opportunity().create().Id;

        }

        public ChiffrageBuilder withOpportunity(Id OpportunityId) {
            this.Chiffrage.Opportunity__c = OpportunityId;
            return this;
        }


        public Chiffrage__c build() {
            return this.Chiffrage;
        }

        public Chiffrage__c create() {
            insert this.Chiffrage;
            return this.Chiffrage;
        }

        public List<Chiffrage__c> createList(Integer count) {
            List<Chiffrage__c> Chiffrages = new List<Chiffrage__c>();
            for (Integer i = 0; i < count; i++) {
                Chiffrage__c ch = build().clone();
                Chiffrages.add(ch);
            }
            insert Chiffrages;
            return Chiffrages;
        }
    }

    public class FostBuilder implements ITestDataBuilder {
        private Product2 fost;

        public FostBuilder() {
            this.fost = new Product2();
            this.fost.Name = 'Fost ' + Utility.generateRandomString(5);
            this.fost.ProductCode = 'Fost ' + Utility.generateRandomString(5);
          
        }
        

        public FostBuilder withName(String name) {
            this.fost.Name = name;
            return this;
        }

        public FostBuilder withProductCode(String productCode) {
            this.fost.ProductCode = productCode;
            return this;
        }

        public Product2 build() {
            return this.fost;
        }

        public Product2 create() {
            insert this.fost;
            return this.fost;
        }

        public List<Product2> createList(Integer count) {
            List<Product2> fests = new List<Product2>();
            for (Integer i = 0; i < count; i++) {
                Product2 fest = build().clone();
                fest.Name += ' ' + (i + 1);
                fest.ProductCode += ' ' + (i + 1);
                fests.add(fest);
            }
            insert fests;
            return fests;
        }
    }

    public class FostConventionBuilder implements ITestDataBuilder {
        private FOST_de_convention__c fostConvention;

        public FostConventionBuilder() {
            this.fostConvention = new FOST_de_convention__c();
            this.fostConvention.Fost__c = TestFactory.fost().create().Id;
            this.fostConvention.Convention__c = TestFactory.contract().create().Id;
        }

        public FostConventionBuilder withFost(Id fostId) {
            this.fostConvention.Fost__c = fostId;
            return this;
        }

        public FostConventionBuilder withConvention(Id conventionId) {
            this.fostConvention.Convention__c = conventionId;
            return this;
        }

        public FOST_de_convention__c build() {
            return this.fostConvention;
        }

        public FOST_de_convention__c create() {
            insert this.fostConvention;
            return this.fostConvention;
        }

        public List<FOST_de_convention__c> createList(Integer count) {
            List<FOST_de_convention__c> fostConventions = new List<FOST_de_convention__c>();
            for (Integer i = 0; i < count; i++) {
                FOST_de_convention__c fc = build().clone();
                fostConventions.add(fc);
            }
            insert fostConventions;
            return fostConventions;
        }
    }

    public class BuilderFicheOperation implements ITestDataBuilder {
        private FicheOperation__c ficheOperation;

        public BuilderFicheOperation() {
            this.ficheOperation = new FicheOperation__c();
            this.ficheOperation.Name = 'Fiche Operation ' + Utility.generateRandomString(5);
            this.ficheOperation.Offre__c = 'CDP'; // Default offer typ
            this.ficheOperation.Secteur__c = 'Secteur A'; // Default sector
            this.ficheOperation.ZoneApplication__c = 'Zone A'; // Default application zone
            this.ficheOperation.isSoumisRGE__c = true; // Default RGE status
          
        }

        public BuilderFicheOperation withName(String name) {
            this.ficheOperation.Name = name;
            return this;
        }

        public BuilderFicheOperation withOffre(String offre) {
            this.ficheOperation.offre__c = offre;
            return this;
        }
        // public BuilderFicheOperation withDateEngagement(Date dateEngagement) {
        //     this.ficheOperation.dateEngagement__c = dateEngagement;
        //     return this;
        // }
        public BuilderFicheOperation withSecteur(String secteur) {
            this.ficheOperation.secteur__c = secteur;
            return this;
        }
        public BuilderFicheOperation withZoneApplication(String zoneApplication) {
            this.ficheOperation.zoneApplication__c = zoneApplication;
            return this;
        }
        public BuilderFicheOperation withIsRGE(Boolean isRGE) {
            this.ficheOperation.isSoumisRGE__c = isRGE;
            return this;
        }

        public FicheOperation__c build() {
            return this.ficheOperation;
        }

        public FicheOperation__c create() {
            insert this.ficheOperation;
            return this.ficheOperation;
        }

        public List<FicheOperation__c> createList(Integer count) {
            List<FicheOperation__c> ficheOperations = new List<FicheOperation__c>();
            for (Integer i = 0; i < count; i++) {
                FicheOperation__c fo = build().clone();
                fo.Name += ' ' + (i + 1);
                fo.offre__c += ' ' + (i + 1);
                // fo.dateEngagement__c = Date.today().addDays(i); // Different engagement dates
                fo.secteur__c += ' ' + (i + 1);
                fo.zoneApplication__c += ' ' + (i + 1);
                
                // Add any other fields that need to be varied  
                ficheOperations.add(fo);
            }
            insert ficheOperations;
            return ficheOperations;
        }
    }

    public class LotEmmyBuilder implements ITestDataBuilder {
        private Lot__c lot;

        public LotEmmyBuilder() {
            this.lot = new Lot__c();
            this.lot.Name = Utility.generateNumberString(5);
        }

        public LotEmmyBuilder withName(String name) {
            this.lot.Name = name;
            return this;
        }

        public Lot__c build() {
            return this.lot;
        }

        public Lot__c create() {
            insert this.lot;
            return this.lot;
        }

        public List<Lot__c> createList(Integer count) {
            List<Lot__c> lots = new List<Lot__c>();
            for (Integer i = 0; i < count; i++) {
                Lot__c lotClone = build().clone();
                lotClone.Name += ' ' + (i + 1);
                lots.add(lotClone);
            }
            insert lots;
            return lots;
        }
    }
    
    public class CofracBuilder implements ITestDataBuilder {
        private COFRAC__c cofrac;

        public CofracBuilder() {
            this.cofrac = new Cofrac__c();
            this.cofrac.Name = Utility.generateNumberString(5);
        }   

        public CofracBuilder withName(String name) {
            this.cofrac.Name = name;
            return this;
        }

        public Cofrac__c build() {
            return this.cofrac;
        }

        public Cofrac__c create() {
            insert this.cofrac;
            return this.cofrac;
        }

        public List<Cofrac__c> createList(Integer count) {
            List<Cofrac__c> cofracs = new List<Cofrac__c>();
            for (Integer i = 0; i < count; i++) {
                Cofrac__c cofracClone = build().clone();
                cofracClone.Name += ' ' + (i + 1);
                cofracs.add(cofracClone);
            }
            insert cofracs;
            return cofracs;
        }
    } 

    public class OperationBuilder implements ITestDataBuilder {
        private Operation__c operation;

        public OperationBuilder() {
            this.operation = new Operation__c();
            this.operation.Dossier__c = TestFactory.dossier().create().Id;
        }

        public OperationBuilder withDossier(Id dossierId) {
            this.operation.Dossier__c = dossierId;
            return this;
        }

        public Operation__c build() {
            return this.operation;
        }

        public Operation__c create() {
            insert this.operation;
            return this.operation;
        }
        
        public List<Operation__c> createList(Integer count) {
            List<Operation__c> operations = new List<Operation__c>();

            for (Integer i = 0; i < count; i++) {
                Operation__c opeClone = build().clone();
                operations.add(opeClone);
            }
            insert operations;
            return operations;
        }
    }    

    public class OperationsCofracBuilder implements ITestDataBuilder {
        private operationsCofrac__c operationCofrac;

        public OperationsCofracBuilder() {
            this.operationCofrac = new operationsCofrac__c();
            this.operationCofrac.Name = Utility.generateNumberString(5);
        }

        public operationsCofrac__c build() {
            return this.operationCofrac;
        }

        public operationsCofrac__c create() {
            insert this.operationCofrac;
            return this.operationCofrac;
        }

        public List<operationsCofrac__c> createList(Integer count) {
            List<operationsCofrac__c> operationsCofracs = new List<operationsCofrac__c>();
            for (Integer i = 0; i < count; i++) {
                operationsCofrac__c opeCofracClone = build().clone();
                 opeCofracClone.Name += ' ' + (i + 1);
                 operationsCofracs.add(opeCofracClone);
            }
            insert operationsCofracs;
            return operationsCofracs;
        }
    }
}