@isTest
private class EM001_FicheOperation_Test {

     @TestSetup
    static void setupTestData() {
        List<FicheOperation__c> ficheOperations = TestFactory.ficheOperation().createList(3);
     }
  @isTest
    static void testGettFicheOperationById() {
      Set<Id> ficheOpeIdSet = new Set<Id>();
        List<FicheOperation__c> ficheOperations = [SELECT Id FROM FicheOperation__c LIMIT 3];
        for (FicheOperation__c ficheOperation : ficheOperations) {
            ficheOpeIdSet.add(ficheOperation.Id);
        }
        Test.startTest();
        List<FicheOperation__c> result = EM001_FicheOperation.getFicheOperationById(ficheOpeIdSet);
        Test.stopTest();
        System.assertEquals(3, result.size(), 'Devrait retourner 3 fiches opérations');
    }
 @isTest 
 static void testGetFicheOperationWithRelatedById(){
      List<FicheOperation__c> ficheOperations = TestFactory.ficheOperation().createList(3);
      Set<Id> ficheOpeIdSet = new Set<Id>();
        for (FicheOperation__c ficheOperation : ficheOperations) {
            ficheOpeIdSet.add(ficheOperation.Id);
        }
        Test.startTest();
        List<FicheOperation__c> result = EM001_FicheOperation.getFicheOperationWithRelatedById(ficheOpeIdSet);
        Test.stopTest();
        System.assertEquals(3, result.size(), 'Devrait retourner 3 fiches opérations');
 }
 @isTest
 public  void testGetficheOperation() {
       List<FicheOperation__c> ficheOperations = TestFactory.ficheOperation().createList(3);
     Set<Id> ficheOpeIdSet = new Set<Id>();
        for (FicheOperation__c ficheOperation : ficheOperations) {
            ficheOpeIdSet.add(ficheOperation.Id);
        }
        Test.startTest();
        List<FicheOperation__c> result = EM001_FicheOperation.getficheOperation('CDP', Date.today(), 'Secteur A', 'Zone A', true);
        Test.stopTest();
        System.assertEquals(3, result.size(), 'Devrait retourner 3 fiches opérations');
 }  
  
}