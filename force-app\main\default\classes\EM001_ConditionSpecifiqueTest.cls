/**
* @File Name : EM001_ConditionSpecifiqueTest
* @Description : Classe de test pour EM001_ConditionSpecifique
* <AUTHOR> Assistant IA
* @Last Modified By :
* @Last Modified On : July 28, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 28, 2025|   | Initial Version
**/
@isTest
private class EM001_ConditionSpecifiqueTest {

    /**
    * @Description      : Test de la méthode getAllCSOfConvention avec des données valides
    * @method Name      : testGetAllCSOfConvention_WithValidData
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetAllCSOfConvention_WithValidData() {
        Test.startTest();
        
        // Création des conventions de test
        Contract parentConvention = TestFactory.contract().create();
        Contract currentConvention = TestFactory.contract().create();
        Contract otherConvention = TestFactory.contract().create();
        
        // Création des conditions spécifiques liées à la convention parent
        ConditionSpecifique__c cs1 = TestFactory.conditionSpecifique()
            .withConvention(otherConvention.Id)
            .withDocumentContractuelOriginale(parentConvention.Id)
            .create();
            
        ConditionSpecifique__c cs2 = TestFactory.conditionSpecifique()
            .withConvention(otherConvention.Id)
            .withDocumentContractuelOriginale(currentConvention.Id)
            .create();
            
        // Condition spécifique qui ne devrait pas être retournée (convention actuelle)
        ConditionSpecifique__c cs3 = TestFactory.conditionSpecifique()
            .withConvention(currentConvention.Id)
            .withDocumentContractuelOriginale(parentConvention.Id)
            .create();
            
        // Condition spécifique sans DocumentContractuelOriginale (ne devrait pas être retournée)
        ConditionSpecifique__c cs4 = TestFactory.conditionSpecifique()
            .withConvention(otherConvention.Id)
            .create();
        
        // Appel de la méthode à tester
        List<ConditionSpecifique__c> result = EM001_ConditionSpecifique.getAllCSOfConvention(
            parentConvention.Id, 
            currentConvention.Id
        );
        
        Test.stopTest();
        
        // Vérifications
        System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
        System.assertEquals(2, result.size(), 'Devrait retourner 2 conditions spécifiques');
        
        // Vérifier que les bonnes conditions sont retournées
        Set<Id> resultIds = new Set<Id>();
        for (ConditionSpecifique__c cs : result) {
            resultIds.add(cs.Id);
            // Vérifier que la convention actuelle n'est pas dans les résultats
            System.assertNotEquals(currentConvention.Id, cs.Convention__c, 
                'La convention actuelle ne devrait pas être dans les résultats');
            // Vérifier que DocumentContractuelOriginale n'est pas null
            System.assertNotEquals(null, cs.DocumentContractuelOriginale__c, 
                'DocumentContractuelOriginale ne devrait pas être null');
        }
        
        // Vérifier que cs1 et cs2 sont dans les résultats
        System.assert(resultIds.contains(cs1.Id), 'cs1 devrait être dans les résultats');
        System.assert(resultIds.contains(cs2.Id), 'cs2 devrait être dans les résultats');
        // Vérifier que cs3 et cs4 ne sont pas dans les résultats
        System.assert(!resultIds.contains(cs3.Id), 'cs3 ne devrait pas être dans les résultats');
        System.assert(!resultIds.contains(cs4.Id), 'cs4 ne devrait pas être dans les résultats');
    }

    /**
    * @Description      : Test de la méthode getAllCSOfConvention avec des paramètres null
    * @method Name      : testGetAllCSOfConvention_WithNullParameters
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetAllCSOfConvention_WithNullParameters() {
        Test.startTest();
        
        // Test avec parentConventionId null
        List<ConditionSpecifique__c> result1 = EM001_ConditionSpecifique.getAllCSOfConvention(
            null, 
            TestFactory.contract().create().Id
        );
        
        // Test avec conventionId null
        List<ConditionSpecifique__c> result2 = EM001_ConditionSpecifique.getAllCSOfConvention(
            TestFactory.contract().create().Id, 
            null
        );
        
        // Test avec les deux paramètres null
        List<ConditionSpecifique__c> result3 = EM001_ConditionSpecifique.getAllCSOfConvention(
            null, 
            null
        );
        
        Test.stopTest();
        
        // Vérifications
        System.assertNotEquals(null, result1, 'Le résultat 1 ne devrait pas être null');
        System.assertEquals(0, result1.size(), 'Le résultat 1 devrait être vide');
        
        System.assertNotEquals(null, result2, 'Le résultat 2 ne devrait pas être null');
        System.assertEquals(0, result2.size(), 'Le résultat 2 devrait être vide');
        
        System.assertNotEquals(null, result3, 'Le résultat 3 ne devrait pas être null');
        System.assertEquals(0, result3.size(), 'Le résultat 3 devrait être vide');
    }

    /**
    * @Description      : Test de la méthode getAllCSOfConvention sans données correspondantes
    * @method Name      : testGetAllCSOfConvention_NoMatchingData
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetAllCSOfConvention_NoMatchingData() {
        Test.startTest();
        
        // Création des conventions de test
        Contract parentConvention = TestFactory.contract().create();
        Contract currentConvention = TestFactory.contract().create();
        Contract unrelatedConvention = TestFactory.contract().create();
        
        // Création d'une condition spécifique non liée aux conventions testées
        ConditionSpecifique__c cs1 = TestFactory.conditionSpecifique()
            .withConvention(unrelatedConvention.Id)
            .withDocumentContractuelOriginale(unrelatedConvention.Id)
            .create();
        
        // Appel de la méthode à tester
        List<ConditionSpecifique__c> result = EM001_ConditionSpecifique.getAllCSOfConvention(
            parentConvention.Id, 
            currentConvention.Id
        );
        
        Test.stopTest();
        
        // Vérifications
        System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
        System.assertEquals(0, result.size(), 'Aucune condition spécifique ne devrait être retournée');
    }

    /**
    * @Description      : Test de la méthode getAllCSOfConvention avec des IDs identiques
    * @method Name      : testGetAllCSOfConvention_SameIds
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetAllCSOfConvention_SameIds() {
        Test.startTest();
        
        // Création d'une convention de test
        Contract convention = TestFactory.contract().create();
        
        // Création d'une condition spécifique
        ConditionSpecifique__c cs1 = TestFactory.conditionSpecifique()
            .withConvention(convention.Id)
            .withDocumentContractuelOriginale(convention.Id)
            .create();
        
        // Appel de la méthode à tester avec les mêmes IDs
        List<ConditionSpecifique__c> result = EM001_ConditionSpecifique.getAllCSOfConvention(
            convention.Id, 
            convention.Id
        );
        
        Test.stopTest();
        
        // Vérifications - la condition ne devrait pas être retournée car Convention__c = conventionId
        System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
        System.assertEquals(0, result.size(), 'Aucune condition ne devrait être retournée car Convention__c = conventionId');
    }

    /**
    * @Description      : Test de la méthode getCSByConvention
    * @method Name      : testGetCSByConvention
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetCSByConvention() {
        Test.startTest();

        // Création d'une convention de test
        Contract convention = TestFactory.contract().create();

        // Création de conditions spécifiques liées à cette convention
        ConditionSpecifique__c cs1 = TestFactory.conditionSpecifique()
            .withConvention(convention.Id)
            .create();

        ConditionSpecifique__c cs2 = TestFactory.conditionSpecifique()
            .withConvention(convention.Id)
            .create();

        // Création d'une condition spécifique liée à une autre convention
        Contract otherConvention = TestFactory.contract().create();
        ConditionSpecifique__c cs3 = TestFactory.conditionSpecifique()
            .withConvention(otherConvention.Id)
            .create();

        // Appel de la méthode à tester
        List<ConditionSpecifique__c> result = EM001_ConditionSpecifique.getCSByConvention(convention.Id);

        Test.stopTest();

        // Vérifications
        System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
        System.assertEquals(2, result.size(), 'Devrait retourner 2 conditions spécifiques');

        // Vérifier que les bonnes conditions sont retournées
        Set<Id> resultIds = new Set<Id>();
        for (ConditionSpecifique__c cs : result) {
            resultIds.add(cs.Id);
            System.assertEquals(convention.Id, cs.Convention__c, 'Toutes les conditions devraient être liées à la convention testée');
        }

        System.assert(resultIds.contains(cs1.Id), 'cs1 devrait être dans les résultats');
        System.assert(resultIds.contains(cs2.Id), 'cs2 devrait être dans les résultats');
        System.assert(!resultIds.contains(cs3.Id), 'cs3 ne devrait pas être dans les résultats');
    }

    /**
    * @Description      : Test de la méthode getCSById
    * @method Name      : testGetCSById
    * <AUTHOR> Assistant IA
    * @date             : July 28, 2025
    * @Modification Log :
    * 1.0       			July 28, 2025				Assistant IA   Création
    * -------------------------------------------------------------------------------------------------
    **/
    @isTest
    static void testGetCSById() {
        Test.startTest();

        // Création d'une condition spécifique de test
        ConditionSpecifique__c cs = TestFactory.conditionSpecifique().create();

        // Appel de la méthode à tester
        ConditionSpecifique__c result = EM001_ConditionSpecifique.getCSById(cs.Id);

        Test.stopTest();

        // Vérifications
        System.assertNotEquals(null, result, 'Le résultat ne devrait pas être null');
        System.assertEquals(cs.Id, result.Id, 'L\'ID de la condition retournée devrait correspondre');
        System.assertEquals(cs.Convention__c, result.Convention__c, 'La convention devrait correspondre');
        System.assertEquals(cs.Dossier__c, result.Dossier__c, 'Le dossier devrait correspondre');
    }
}
