@isTest
private class Test_GestionGrilleCtrl {
    @TestSetup
    static void setupTestData() {
        // Création de données de test pour les grilles tarifaires
        List<GrilleTarifaire__c> grilles = TestFactory.grilleTarifaire().createList(3);
        List<LigneGrilleTarifiare__c> lignes = TestFactory.ligneGrilleTarifaire().createList(3);
         GrilleTarifaire__c grille = TestFactory.grilleTarifaire().create();
        //GrilleTarifaire__c Grille=TestFactory.grilleTarifaire().create();
    }
    @isTest
    static void testGetGrillesTarifaires(){
      
       Contract convention=TestFactory.contract().create();
        List<GrilleTarifaire__c> grilles = [SELECT Id, Name FROM GrilleTarifaire__c WHERE Convention__c = :convention.Id];
        Test.startTest();
        List<GrilleTarifaire__c> res = Lwc001_GestionGrilleTarifaireController.getGrillesTarifaires(convention.Id);
        Test.stopTest();

        Utility.assertExpectedIdsInResults(grilles, res);
        
    }
@isTest
static void testGetGrillesTarifaires_NullContract() {
    Id contractId = null; // C'est ce que la méthode attend

    try {
        Test.startTest();
        Lwc001_GestionGrilleTarifaireController.getGrillesTarifaires(contractId);
        Test.stopTest();

        System.assert(false, 'Une exception AuraHandledException aurait dû être levée');

    } catch (AuraHandledException e) {
        System.debug('Exception capturée : ' + e.getMessage());
        System.assert(true, 'Exception correctement capturée');

    } catch (Exception e) {
        System.assert(false, 'Une AuraHandledException était attendue, mais une autre exception a été levée : ' + e.getMessage());
    }
}


        
   
        
    
     /*@isTest
    static void testGetGrillesTarifaires_withNullContract() {
        // Cas où le contrat est null
        Contract convention = null;

        Test.startTest();
        List<GrilleTarifaire__c> res = Lwc001_GestionGrilleTarifaireController.getGrillesTarifaires(convention != null ? convention.Id : null );
        Test.stopTest();

        // On vérifie que la liste retournée est vide ou appropriée selon ton code
        System.assertEquals(0, res.size(), 'La liste doit être vide si le contrat est null.');
    }*/
   /* @isTest
    static void TestCreateGrilleTarifaire(){
        GrilleTarifaire__c grille=TestFactory.grilleTarifaire().create();
        Test.startTest();
        GrilleTarifaire__c res = Lwc001_GestionGrilleTarifaireController.createGrilleTarifaire(grille);
        Test.stopTest();
          System.assertNotEquals(null, res, 'La grille retournée ne doit pas être null');
    }*/
    @isTest
  static void TestCreateGrilleTarifaire(){
    // Étape 1 : Créer une instance sans insert
    GrilleTarifaire__c originalGrille = TestFactory.grilleTarifaire().build();

    // Étape 2 : Passer la grille à la méthode (elle sera insérée dans le contrôleur)
    Test.startTest();
    GrilleTarifaire__c res = Lwc001_GestionGrilleTarifaireController.createGrilleTarifaire(originalGrille);
    Test.stopTest();

    // Étape 3 : Vérification
    System.assertNotEquals(null, res, 'La grille retournée ne doit pas être null');
    System.assertNotEquals(null, res.Id, 'La grille doit avoir été insérée et avoir un Id');
}
   @isTest
   static void TestCreateGrilleTarifaire_NullGrille() {
    GrilleTarifaire__c grilleNull = null;

    try {
        Test.startTest();
        Lwc001_GestionGrilleTarifaireController.createGrilleTarifaire(grilleNull);
        Test.stopTest();

        System.assert(false, 'Une exception AuraHandledException aurait dû être levée');
    } catch (AuraHandledException e) {
        System.debug('Exception capturée : ' + e.getMessage());
        // On ne vérifie pas le message car il est masqué
        System.assert(true, 'Exception correctement capturée');
    } catch (Exception e) {
        System.assert(false, 'Une AuraHandledException était attendue, mais une autre exception a été levée : ' + e.getMessage());
    }
     
//ligneGrilleTarifaire
    
    
}

 @isTest
static void TestSaveLignesTarifaires() {
   GrilleTarifaire__c grille = [SELECT Id FROM GrilleTarifaire__c LIMIT 1];
   List<LigneGrilleTarifiare__c> lignes = [SELECT Id FROM LigneGrilleTarifiare__c LIMIT 3];
   Test.startTest();
    List<LigneGrilleTarifiare__c> result = Lwc001_GestionGrilleTarifaireController.saveLignesTarifaires(lignes, grille.Id);
    Test.stopTest();
    System.assertNotEquals(null, result, 'La liste de lignes tarifaires retournée ne doit pas être null');
    System.assertEquals(lignes.size(), result.size(), 'Le nombre de lignes tarifaires retournées doit correspondre au nombre de lignes insérées');
}

 @isTest
 static void testSupprimerGrilleTarifaire(){
    GrilleTarifaire__c grille = [SELECT Id FROM GrilleTarifaire__c LIMIT 1];
    Test.startTest();
    Lwc001_GestionGrilleTarifaireController.supprimerGrilleTarifaire(grille.Id);
    Test.stopTest();    
    // Vérification que la grille a été supprimée
    GrilleTarifaire__c deletedGrille = [SELECT Id FROM GrilleTarifaire__c WHERE Id = :grille.Id ALL ROWS];
    System.assertEquals(null, deletedGrille, 'La grille tarifaire devrait être supprimée');
  } 
  
 }
        