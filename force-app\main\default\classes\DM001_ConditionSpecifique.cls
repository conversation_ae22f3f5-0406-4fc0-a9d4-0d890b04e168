/**
* @File Name : DM001_FostDeConvention
* @Description : DM de l'objet ConditionSpecifique__c
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 10, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 10, 2025|   | Initial Version
**/
public with sharing class DM001_ConditionSpecifique {
    /**
    * @Description      :   Cette methode permet récupérer les conditions par conventionId
    * @method Name      :   selectCSByConvention
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   conventionId => Id de convention
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> selectCSByConvention(Id conventionId){
          return [
                       SELECT Id, Name, 
                       Dossier__c, Dossier__r.Name,
                       Opportunite__c, Opportunite__r.Name,
                       Type__c,
                       VolumePrevitionnelClassique__c,
                       VolumePrevisionnelPrecaire__c,
                       VolumePrevisionnelTotal__c,
                       DatePrePreuveEngagement__c,
                       DatePreFinDeTravaux__c,
                       PrixClassique__c,
                       PrixPrecaire__c,
                       ForfaitClassique__c,
                       ForfaitPrecaire__c,
                       CommissionClassique__c,
                       CommissionPrecaire__c,
                       MontantRemuneration__c,
                       MontantRemunerationRepartition__c,
                       Repartition__c,
                       Convention__c,
                       DocumentContractuelOriginale__c,
                       CreatedDate,
                       TypeDeTravaux__c,
                       LastModifiedDate,
                       PrixClassiqueInstallateur__c,
                       PrixPrecaireInstallateur__c
                FROM ConditionSpecifique__c
                WHERE Convention__c = :conventionId
                ORDER BY CreatedDate DESC
            ];
    }

     /**
    * @Description      :   Cette methode permet récupérer les coditions par son ID
    * @method Name      :   selectCSById
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   fostConventionId => Id du fost de convention
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static ConditionSpecifique__c selectCSById(Id conditionId){
          return [
                 SELECT Id,Dossier__c, Opportunite__c, Convention__c, Convention__r.Status
                FROM ConditionSpecifique__c 
                WHERE Id = :conditionId 
                LIMIT 1
            ];
    }

     /**
    * @Description      :   Cette methode permet récupérer les conditions par le dossier l'opportunité et la convention
    * @method Name      :   getCSByDossierOppConvention
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   conventionId => Id de convention
    * @Param            :   dossierId => Id du dossier
    * @Param            :   opportunityId => Ie l'opportunite
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> getCSByDossierOppConvention(Id conventionId,Id dossierId,Id opportunityId){
          return [
                SELECT Id 
                FROM ConditionSpecifique__c 
                WHERE Convention__c = :conventionId 
                AND Dossier__c = :dossierId 
                AND Opportunite__c = :opportunityId 
                LIMIT 1
            ];
    }

     /**
    * @Description      :   Cette methode permet récupérer toutes les conditions spécifiques associé à une convention(ne pas recupérer la convention atuelle)
    * @method Name      :   selectAllCSOfConvention
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   parentConventionId => Id de convention
    * @Param            :   conventionId => Id de convention parent
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> selectAllCSOfConvention(Id parentConventionId,Id conventionId){
          return [
                 SELECT Id, Name,
                       Dossier__c, Dossier__r.Name,
                       Opportunite__c, Opportunite__r.Name,
                       Type__c,
                       VolumePrevitionnelClassique__c,
                       VolumePrevisionnelPrecaire__c,
                       VolumePrevisionnelTotal__c,
                       DatePrePreuveEngagement__c,
                       DatePreFinDeTravaux__c,
                       PrixClassique__c,
                       PrixPrecaire__c,
                       ForfaitClassique__c,
                       ForfaitPrecaire__c,
                       CommissionClassique__c,
                       CommissionPrecaire__c,
                       MontantRemuneration__c,
                       MontantRemunerationRepartition__c,
                       Repartition__c,
                       Convention__c,
                       DocumentContractuelOriginale__c,
                       CreatedDate,
                       TypeDeTravaux__c,
                       LastModifiedDate,
                       Convention__r.Version__c,
                       PrixClassiqueInstallateur__c,
                       PrixPrecaireInstallateur__c
                FROM ConditionSpecifique__c
                WHERE (DocumentContractuelOriginale__c = :parentConventionId 
                       OR DocumentContractuelOriginale__c = :conventionId) 
                AND DocumentContractuelOriginale__c != null
                AND Convention__c != :conventionId
                ORDER BY CreatedDate DESC
            ];
    }

    /**
    * @Description      :   Cette methode permet récupérer toutes les conditions spécifiques associé à une convention(convention actuelle inclus)
    * @method Name      :   selectAllCSOfConvention_V2
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   parentConventionId => Id de convention
    * @Param            :   conventionId => Id de convention parent
    * @Modification Log :
    * 1.0       			July 10, 2025			Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ConditionSpecifique__c> selectAllCSOfConvention_V2(Id parentConventionId,Id conventionId){
          return [
                SELECT 
                       Id, Name, 
                       Dossier__c, Dossier__r.Name,
                       Opportunite__c, Opportunite__r.Name,
                       Type__c,
                       VolumePrevitionnelClassique__c,
                       VolumePrevisionnelPrecaire__c,
                       VolumePrevisionnelTotal__c,
                       DatePrePreuveEngagement__c,
                       DatePreFinDeTravaux__c,
                       PrixClassique__c,
                       PrixPrecaire__c,
                       ForfaitClassique__c,
                       ForfaitPrecaire__c,
                       CommissionClassique__c,
                       CommissionPrecaire__c,
                       MontantRemuneration__c,
                       MontantRemunerationRepartition__c,
                       Repartition__c,
                       Convention__c,
                       DocumentContractuelOriginale__c,
                       CreatedDate,
                       TypeDeTravaux__c,
                       LastModifiedDate,
                       Convention__r.Version__c,
                       Convention__r.Name,
                       Convention__r.Id,
                       PrixClassiqueInstallateur__c,
                       PrixPrecaireInstallateur__c
                FROM ConditionSpecifique__c
                WHERE (DocumentContractuelOriginale__c = :parentConventionId or DocumentContractuelOriginale__c =:conventionId) and DocumentContractuelOriginale__c!=null
                ORDER BY CreatedDate DESC
            ];
    }
  
}