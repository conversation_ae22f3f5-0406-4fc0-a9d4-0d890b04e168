@isTest
private class TestOpportunityController {

    @isTest static void testStoredEvent1() {
        
        EventPublisher.storeEvents = true;

        Test.startTest();

        String oppName = 'TestOpportunityController';
        Opportunity opp1 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        Opportunity opp2 = new Opportunity(Name=oppName, Valo_globale_classique__c=2000, Valo_globale_precaire__c=2000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        
        Opportunity[] oldOpps = new Opportunity[] {opp1};
        Opportunity[] newOpps = new Opportunity[] {opp2};

        OpportunityTriggerController.triggerOpportunities(oldOpps, newOpps);

        Test.stopTest();

        List<SObject> events = EventPublisher.eventsByName.get('ITBRM_Opportunity_valo_globale_update__e');
        System.assert( events != null);
        System.assertEquals(1, events.size());

        // Clear events
        EventPublisher.eventsByName.clear();
    }

    @isTest static void testStoredEvent2() {
        
        EventPublisher.storeEvents = true;

        Test.startTest();

        String oppName = 'TestOpportunityController';
        Opportunity opp1 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        Opportunity opp2 = new Opportunity(Name=oppName, Valo_globale_classique__c=2000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        
        Opportunity[] oldOpps = new Opportunity[] {opp1};
        Opportunity[] newOpps = new Opportunity[] {opp2};

        OpportunityTriggerController.triggerOpportunities(oldOpps, newOpps);

        Test.stopTest();

        List<SObject> events = EventPublisher.eventsByName.get('ITBRM_Opportunity_valo_globale_update__e');
        System.assert( events != null);
        System.assertEquals(1, events.size());

        // Clear events
        EventPublisher.eventsByName.clear();
    
    }

    @isTest static void testStoredEvent3() {
        
        EventPublisher.storeEvents = true;

        Test.startTest();

        String oppName = 'TestOpportunityController';
        Opportunity opp1 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        Opportunity opp2 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=2000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        
        Opportunity[] oldOpps = new Opportunity[] {opp1};
        Opportunity[] newOpps = new Opportunity[] {opp2};

        OpportunityTriggerController.triggerOpportunities(oldOpps, newOpps);

        Test.stopTest();

        List<SObject> events = EventPublisher.eventsByName.get('ITBRM_Opportunity_valo_globale_update__e');
        System.assert( events != null);
        System.assertEquals(1, events.size());

        // Clear events
        EventPublisher.eventsByName.clear();
    
    }

    @isTest static void testStoredEvent4() {
        
        EventPublisher.storeEvents = true;

        Test.startTest();

        String oppName = 'TestOpportunityController';
        Opportunity opp1 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        Opportunity opp2 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        
        Opportunity[] oldOpps = new Opportunity[] {opp1};
        Opportunity[] newOpps = new Opportunity[] {opp2};

        OpportunityTriggerController.triggerOpportunities(oldOpps, newOpps);

        Test.stopTest();

        List<SObject> events = EventPublisher.eventsByName.get('ITBRM_Opportunity_valo_globale_update__e');
        System.assert( events == null);

        // Clear events
        EventPublisher.eventsByName.clear();
    
    }

    @isTest static void testWithoutEventStorage() {
        
        EventPublisher.storeEvents = false;

        Test.startTest();
        String oppName = 'TestOpportunityController';
        Opportunity opp1 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        Opportunity opp2 = new Opportunity(Name=oppName, Valo_globale_classique__c=2000, Valo_globale_precaire__c=2000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        
        Opportunity[] oldOpps = new Opportunity[] {opp1};
        Opportunity[] newOpps = new Opportunity[] {opp2};

        OpportunityTriggerController.triggerOpportunities(oldOpps, newOpps);
        // String oppName = 'TestOpportunityController';
        // Opportunity opp1 = new Opportunity(Name=oppName, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10));
        // insert opp1;

        // Opportunity oppFromSF = [
        //     SELECT Id, Name, Valo_globale_classique__c, Valo_globale_precaire__c
        //     FROM Opportunity 
        //     WHERE Name=:oppName
        //     LIMIT 1];

        // oppFromSF.Valo_globale_classique__c = 2000;
        // oppFromSF.Valo_globale_precaire__c = 2000;
        // update oppFromSF;

        Test.stopTest();

        List<SObject> events = EventPublisher.eventsByName.get('ITBRM_Opportunity_valo_globale_update__e');
        System.assert( events == null);
    }


    @isTest static void testOpportunityTrigger() {
        
        EventPublisher.storeEvents = false;

        Test.startTest();


        String oppRecordType = 'Detection';
        Id rtId = Schema.SObjectType.Opportunity.getRecordTypeInfosByDeveloperName().get(oppRecordType).getRecordTypeId();


        Id accountRtId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Professionnel').getRecordTypeId();
        Account newAccount = new Account(Name='Account1', RecordTypeId=accountRtId, Date_de_validation_fiche_signaletique__c=System.today());
        insert newAccount;

        Id accountId = getAccount('Account1').Id;

        Id contractRtId = Schema.SObjectType.Contract.getRecordTypeInfosByDeveloperName().get('Beneficiaire').getRecordTypeId();
        Contract contract = new Contract(Name='Contract1', RecordTypeId=contractRtId, Status='Brouillon', StartDate=Date.newInstance(2020,6,10), ContractTerm=1, AccountId=accountId, Mode_de_reconduction__c='Tacite');
        insert contract;

        Contract contractFromSF = getContract('Contract1');
        contractFromSF.Approuv_e__c = true;
        update contractFromSF;

        contractFromSF = getContract('Contract1');
        contractFromSF.Status = 'Activee';
        update contractFromSF;

        Id contractId = contractFromSF.Id;

        String oppName = 'TestOpportunityController2';
        Opportunity opp1 = new Opportunity(Name=oppName, AccountId=accountId, Valo_globale_classique__c=1000, Valo_globale_precaire__c=1000, StageName='Offre à envoyer', CloseDate=Date.newInstance(2020,7,10), RecordTypeId=rtId, Forfait_calcule__c=true, Offre_envoyee__c=false, ContractId=contractId, Date_d_but_th_orique__c=Date.newInstance(2020,7,10), Date_fin_th_orique__c=Date.newInstance(2020,7,10));
        insert opp1;
        

        // C+M Opportunity Process performs opportunity updates
        // So we do not need to update the opportuntity by hand
        // Which is a good thing, since it does not work. Why ???
        // 

        // Opportunity oppFromSF = [
        //     SELECT Id, Name, Valo_globale_classique__c, Valo_globale_precaire__c
        //     FROM Opportunity 
        //     WHERE Name=:oppName
        //     LIMIT 1];

        // oppFromSF.Valo_globale_classique__c = 2000;
        // oppFromSF.Valo_globale_precaire__c = 2000;
        // update oppFromSF;

        Test.stopTest();

        List<SObject> events = EventPublisher.eventsByName.get('ITBRM_Opportunity_valo_globale_update__e');
        System.assert( events == null);
    }

    public static Account getAccount(String name) {
    
        return [
            SELECT Id, Name
            FROM Account
            WHERE Name = :name
            LIMIT 1
        ];
    }


    public static Contract getContract(String name) {
    
        return [
            SELECT Id, Name, Status, Approuv_e__c
            FROM Contract
            WHERE Name = :name
            LIMIT 1
        ];
    }
}