/**
* @File Name : EM011_AdresseAssociee
* @Description : EM de l'objet AdresseAssociee__C
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 10, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 10, 2025|   | Initial Version
**/
public with sharing class EM011_AdresseAssociee {
    /**
    * @Description      :   Cette methode permet récupérer les lieu de travaux par compte
    * @method Name      :   selectAdresseAssocieByAccount
    * <AUTHOR>   <PERSON>ina <PERSON>EN<PERSON>DELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   beneficiaireIds => Id du compte 
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<AdresseAssociee__c> getAdresseAssocieByAccount(Set<Id> compteIds){
          return DM011_AdresseAssociee.selectAdresseAssocieByAccount(compteIds); 
    }
}