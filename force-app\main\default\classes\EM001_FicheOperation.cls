/**
* @File Name : EM001_FicheOperation.cls
* @Description :
* <AUTHOR>
* @Last Modified By : 🦅  | DA-technologies
* @Last Modified On : 10-07-2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
**/

public with sharing class EM001_FicheOperation {
	
	private static final DM001_FicheOperation dataManager = new DM001_FicheOperation();
	
	/**
	* <AUTHOR>  | DA-technologies
	* @since 
	* @description Méthode pour récuperer toutes les fiches opérations par leur Id
	* @param ficheOpeIdSet Set ID des fiches opérations
	* @return Liste de fiches opérations liées
	*/
	public static List<FicheOperation__c> getFicheOperationById(Set<Id> ficheOpeIdSet) {
		if (ficheOpeIdSet.size() > 0) {
			return dataManager.getFicheOperationById(ficheOpeIdSet);
		} else {
			return null;
		}
	}
	
	/**
	* <AUTHOR>  | DA-technologies
	* @since 
	* @description Méthode pour récuperer toutes les fiches opérations par leur Id, et les criteres liées
	* @param ficheOpeIdSet Set ID des fiches opérations
	* @return Liste de fiches opérations liées
	*/
	public static List<FicheOperation__c> getFicheOperationWithRelatedById(Set<Id> ficheOpeIdSet) {
		if (ficheOpeIdSet.size() > 0) {
			return dataManager.getFicheOperationWithRelatedById(ficheOpeIdSet);
		} else {
			return null;
		}
	
	}

	/**
	* <AUTHOR>  | DA-technologies
	* @since 2023-07-14 Created
	* @description Méthode pour récuperer toutes les fiches opérations par leur Id, et les criteres liées
	* @param ficheOpeIdSet Set ID des fiches opérations
	* @return Liste de fiches opérations liées
	*/
	public static List<FicheOperation__c> getficheOperation(String offre, Date dateEngagement, String secteur, String zoneApplication, Boolean isRGE) {
		if (offre != null && dateEngagement != null && secteur != null && zoneApplication != null && isRGE != null) {
			return dataManager.getficheOperation(offre, dateEngagement, secteur, zoneApplication, isRGE);
		} else {
			return null;
		}
	
	}

}