/**
* @File Name : DM007_GrilleTarifaire
* @Description : DM de l'objet GrilleTarifaire__c
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 10, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 10, 2025|   | Initial Version
**/
public with sharing class DM007_GrilleTarifaire {
     /**
    * @Description      :   Cette methode permet récupérer les grilles tarifaires associé à une convention 
    * @method Name      :   selectGrilleTarifaireByConvention
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   conventionId => Id du convention
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<GrilleTarifaire__c> selectGrilleTarifaireByConvention(Id conventionId){
          return [ SELECT Id, Name, De__c, A__c, PrixClassique__c, PrixPrecaire__c,Convention__c, CreatedDate, LastModifiedDate,CreatedBy.Name, LastModifiedBy.Name
                FROM  GrilleTarifaire__c
                WHERE Convention__c = :conventionId 
                ORDER BY De__c ASC];     
    }

     /**
    * @Description      :   Cette methode permet récupérer les grilles tarifaire par son Id 
    * @method Name      :   selectGrilleTarifaireById
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   grilleId => Id de la grille tarifaire
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static GrilleTarifaire__c selectGrilleTarifaireById(Id grilleId){
          return [SELECT Id, Name, Convention__c, De__c, A__c, PrixClassique__c, PrixPrecaire__c
                FROM GrilleTarifaire__c 
                WHERE Id = :grilleId 
                LIMIT 1];     
    }
   
}