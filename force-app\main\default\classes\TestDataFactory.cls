/**
 * TestDataFactory : Crée des données de test complètes.
 * - Crée un User avec ByPass_Flow__c = true pour bypasser les flows.
 * - Crée un Account avec le record type "Beneficiaire".
 * - Crée un Contact lié à cet Account avec le record type "Particulier".
 * - Crée une Opportunity liée à cet Account avec le record type "AE".
 * - Crée un Product (FOST) et l'ajoute à la standard Pricebook.
 */
public class TestDataFactory {
    
    public static User createUser() {
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];
        User u = new User(
            Username = 'testuser' + DateTime.now().getTime() + '@example.com',
            Alias = 'tuser',
            Email = '<EMAIL>',
            LastName = 'User',
            ProfileId = p.Id,
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US'
        );
        u.ByPass_Flow__c = true;
        // Ajouter le bypass trigger aussi si le champ existe
        try {
            u.put('ByPass_Trigger__c', false); // false par défaut sauf si besoin spécifique
        } catch(Exception e) {
            // Le champ n'existe pas, on continue
        }
        insert u;
        return u;
    }
    
    public static Account createAccount() {
        Account acc = new Account();
        acc.Name = 'Test Account' + String.valueOf(System.currentTimeMillis());
        acc.Siret__c = '**************';
        // Record type "Beneficiaire" pour Account
        RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Account' AND DeveloperName = 'Beneficiaire' LIMIT 1];
        acc.RecordTypeId = rt.Id;
        insert acc;
        return acc;
    }
    
    public static Contact createContact(Id accountId) {
        Contact c = new Contact();
        c.AccountId = accountId;
        c.LastName = 'Test Contact' + String.valueOf(System.currentTimeMillis());
        // Record type "Particulier" pour Contact
        RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Contact' AND DeveloperName = 'Particulier' LIMIT 1];
        c.RecordTypeId = rt.Id;
        insert c;
        return c;
    }
    
    public static Opportunity createOpportunity(Id accountId) {
        Opportunity opp = new Opportunity();
        opp.Name = 'Test Opportunity' + String.valueOf(System.currentTimeMillis());
        opp.AccountId = accountId;
        opp.StageName = 'Prospecting';
        opp.CloseDate = Date.today().addDays(30);
        opp.date_pr_visionnel_de_signature_devis__c = Date.today();
        // Record type "AE" pour Opportunity
        RecordType rt = [SELECT Id FROM RecordType WHERE SobjectType = 'Opportunity' AND DeveloperName = 'AE' LIMIT 1];
        opp.RecordTypeId = rt.Id;
        insert opp;
        return opp;
    }
    
    public static Product2 createProduct() {
        Product2 prod = new Product2();
        prod.Name = 'FOST';
        prod.IsActive = true;
        insert prod;
        return prod;
    }
    
    public static PricebookEntry addProductToStandardPricebook(Product2 prod) {
        Id stdPbId;
        if (Test.isRunningTest()) {
            stdPbId = Test.getStandardPricebookId();
        } else {
            stdPbId = [SELECT Id FROM Pricebook2 WHERE IsStandard = true LIMIT 1].Id;
        }
        PricebookEntry pbe = new PricebookEntry();
        pbe.Pricebook2Id = stdPbId;
        pbe.Product2Id = prod.Id;
        pbe.UnitPrice = 100.0;
        pbe.IsActive = true;
        insert pbe;
        return pbe;
    }
    
    /**
     * Méthode principale pour créer toutes les données de test.
     * La création est exécutée dans le contexte de l'utilisateur ayant ByPass_Flow__c à true.
     */
    public static void createAllTestData() {
        User u = createUser();
        System.runAs(u) {
            Account acc = createAccount();
            createContact(acc.Id);
            createOpportunity(acc.Id);
            Product2 prod = createProduct();
            addProductToStandardPricebook(prod);
        }
    }
    
    public static Package__c createPackage() {
        Package__c pkg = new Package__c(Name = 'Test Package ' + String.valueOf(System.currentTimeMillis()));
        insert pkg;
        return pkg;
    }
    
    public static Package_Line__c createPackageLine(Package__c pkg, Id productId, Decimal quantity) {
        Package_Line__c pkgLine = new Package_Line__c(Package__c = pkg.Id, Product__c = productId, Quantity__c = quantity);
        insert pkgLine;
        return pkgLine;
    }
        
    public static List<OpportunityLineItem> createOpportunityLineItemsForRenumber() {
        Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];
        PricebookEntry pbe = [SELECT Id FROM PricebookEntry LIMIT 1];
        List<OpportunityLineItem> olis = new List<OpportunityLineItem>();
        String uniqueSuffix = String.valueOf(System.currentTimeMillis());
        for (Integer i = 0; i < 3; i++) {
            OpportunityLineItem oli = new OpportunityLineItem();
            oli.OpportunityId = opp.Id;
            oli.PricebookEntryId = pbe.Id;
            oli.Quantity = 1;
            oli.UnitPrice = 100;
            oli.Product_Display_Name__c = 'Test Product ' + uniqueSuffix + ' ' + i;
            olis.add(oli);
        }
        insert olis;
        return olis;
    }
         
    public static OpportunityLineItem createOpportunityLineItemForSplit() {
        Opportunity opp = [SELECT Id FROM Opportunity LIMIT 1];
        PricebookEntry pbe = [SELECT Id FROM PricebookEntry LIMIT 1];
        OpportunityLineItem oli = new OpportunityLineItem();
        oli.OpportunityId = opp.Id;
        oli.PricebookEntryId = pbe.Id;
        oli.Quantity = 3;
        oli.UnitPrice = 100;
        String uniqueSuffix = String.valueOf(System.currentTimeMillis());
        oli.Product_Display_Name__c = 'FOST Product ' + uniqueSuffix;
        insert oli;
        return oli;
    }

    public static Chiffrage__c createChiffrage() {
        Chiffrage__c ch = new Chiffrage__c(
            Code_Postal_des_Travaux__c       = 75000,
            Valo_globale_classique_GWHc__c   = 100,
            Mode_de_remuneration__c          = '%', // Utiliser % comme valeur
            Beneficiaire__c                  = 0,
            Professionnel__c                 = 0,
            Convention__c                    = 0,
            Apporteur_d_affaires__c          = 0
        );
        insert ch;
        return ch;
    }

    public static List<ChiffrageLineItem__c> createChiffrageLineItems(Id chiffrageId, Integer count) {
        List<ChiffrageLineItem__c> items = new List<ChiffrageLineItem__c>();
        for (Integer i = 0; i < (count != null && count > 0 ? count : 1); i++) {
            items.add(new ChiffrageLineItem__c(
                Chiffrage__c                          = chiffrageId,
                Name                                  = 'Item ' + i,
                Description_de_la_bonification__c     = 'Desc ' + i,
                Description_des_travaux__c            = 'Travaux ' + i,
                Famille_de_la_bonification__c         = 'Famille ' + i,
                Nom_interne_de_la_bonification__c     = 'Interne ' + i,
                Nom_officiel_de_la_bonification__c    = 'Officiel ' + i,
                Numerotation__c                       = String.valueOf(i + 1),
                Payload__c                            = '{}',
                Volume_GWHC__c                        = 1.0
            ));
        }
        insert items;
        return items;
    }    

    
}