/**
* @File Name : DM001_FicheOperation.cls
* @Description :
* <AUTHOR>
* @Last Modified By : 🦅  | DA-technologies
* @Last Modified On : 10-07-2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | May 25, 2025 |   | Initial Version
**/

public with sharing class DM001_FicheOperation {
	
	/**
	* <AUTHOR>  | DA-technologies
	* @since 2025-07-04 Created
	* @description Méthode pour récupérer toutes les fiches opérations par leur Id
	* @param ficheOpeIdSet Set ID des fiches operations
	* @return Liste de fiches opérations liées
	*/
	public List<FicheOperation__c> getFicheOperationById(Set<Id> ficheOpeIdSet) {
		return [
			SELECT
				Id, IdExterne__c, ParametrageFiche__c, Name, Version__c, Offre__c, Description__c,MultiPointsSinguliers__c, Multi__c, EtatsRecapitulatif__c, FicheManuelle__c
			FROM
				FicheOperation__c
			WHERE
				Id IN :ficheOpeIdSet
		];
	}
	
	/**
	* <AUTHOR>  | DA-technologies
	* @since 2025-07-06 Created
	* @description Méthode pour récupérer toutes les fiches opérations par leur Id, et les objets enfants liées
	* @param ficheOpeIdSet Set ID des fiches operations
	* @return Liste de fiches opérations liées
	*/
	//START EEV2-1039
	public List<FicheOperation__c> getFicheOperationWithRelatedById(Set<Id> ficheOpeIdSet) {
		return [
			SELECT
				Id, IdExterne__c, ParametrageFiche__c, ParametrageAH__c, Name, Version__c, Offre__c, Secteur__c, CoefficientCumac__c, FicheParent__r.estMultiple__c,FicheParent__r.labelMultiple__c,
				EtatsRecapitulatif__c, MultiPointsSinguliers__c, Multi__c,FicheParent__r.Multi__c, ParametrageAHCadreB__c, ParametrageAHCadreQPV__c, FicheManuelle__c,
				//Les combinaisons criteres
				//END EEV2-1039
				(
					SELECT
						Id, CombinaisonInvalide__c, IdentifiantCombinaison__c, ValeurCumac__c
					FROM
						CombinaisonCriteres__r
				),
				//Les criteres
				(
					SELECT
						Id, LibelleCritere__c, IdExterne__c, NomCritere__c, LibelleSaisie__c,
						//les champs criteres
						Critere__c, Critere__r.Nom__c, Critere__r.IdExterne__c, Critere__r.Libelle__c, Critere__r.Type__c,
						Critere__r.Valeur__c, Valeur__c
					FROM
						FichesOperationsCriteres__r
				)
			FROM
				FicheOperation__c
			WHERE
				Id IN :ficheOpeIdSet
		];
	}


	/**
	 * <AUTHOR>  | DA-technologies
	 * @since 2025-07-08 Created
	 * @description Méthode pour récupérer toutes les fiches opérations sélectionnable par l'utilisateur selon l'offre et la liste des secteurs
	 * @param ficheOpeIdSet un set d'id d'opérations
	 * @param offre Offre de la fiche opération
	 * @param dateEngagement la date d'engagement du dossier
	 * @return Liste de fiches opérations
	*/
	public List<FicheOperation__c> getficheOperation(String offre, Date dateEngagement, String secteur, String zoneApplication, Boolean isRGE) {
		System.debug('getficheOperation');
		return [
			SELECT Id, Name, Offre__c, Secteur__c, DateDebutApplication__c, DateFinApplication__c, Description__c, ZoneApplication__c,
				Version__c, MultiPointsSinguliers__c, Multi__c, FicheManuelle__c, (SELECT ContentDocumentId, ContentDocument.LatestPublishedVersionId, ContentDocument.Title FROM ContentDocumentLinks)
			FROM FicheOperation__c
			WHERE Offre__c = :offre
            AND DateDebutApplication__c <= :dateEngagement
            AND (DateFinApplication__c >= :dateEngagement OR DateFinApplication__c = null)
			AND Secteur__c = :secteur
			AND IsSoumisRGE__c = :isRGE
			AND ZoneApplication__c includes (:zoneApplication)
			AND Statut__c = 'Active'
		];
	}


    /**
	* @description 
	* <AUTHOR>  | DA-technologies | 02-06-2025 
	* @param ficheParent 
	* @param dateEngagement 
	* @param typeOffre : CDP ou STD
	* @return FicheOperation__c 
	**/
	@AuraEnabled
	public static FicheOperation__c getFicheVersionByFiche(String ficheParent, String dateEngagement, String typeOffre) {
		try {
			Date engagementDate;

			if (String.isBlank(dateEngagement)) {
            	return new FicheOperation__c();
        	}
			try {
				engagementDate = Date.valueOf(dateEngagement);
			} catch (Exception ex) {
				throw new AuraHandledException('Invalid date format: ' + dateEngagement);
			}
			typeOffre = typeOffre ?? 'STD';

			List<FicheOperation__c> ficheVersion = [
				SELECT Id,Multi__c,MultiPointsSinguliers__c,FicheManuelle__c,Name,Description__c,Version__c,Offre__c,FicheParent__r.Multi__c,FicheParent__r.Name
				FROM FicheOperation__c 
				WHERE FicheParent__c = :ficheParent
				AND DateDebutApplication__c <= :engagementDate
				AND (DateFinApplication__c >= :engagementDate OR DateFinApplication__c = null)
				AND Offre__c=:typeOffre
				LIMIT 1
			];

			return (ficheVersion!=null && !ficheVersion.isEmpty()) ? ficheVersion[0] : new FicheOperation__c() ;

		} catch (Exception e) {
			throw new AuraHandledException('Erreur lors de la récupération de la fiche : ' + e.getMessage());
		}
	}


	/**
	* @description 
	* <AUTHOR>  | DA-technologies | 02-06-2025 
	* @param ficheParent 
	* @return Boolean 
	**/
	@AuraEnabled
	public static Boolean checkCDPoffreOfFicheCEE(String ficheParent) {
		try {

			List<FicheOperation__c> ficheVersion = [
				SELECT Id,Offre__c 
				FROM FicheOperation__c 
				WHERE FicheParent__c = :ficheParent
				AND Offre__c='CDP'
				LIMIT 1
			];

			return (ficheVersion!=null && !ficheVersion.isEmpty()) ? true : false ;

		} catch (Exception e) {
			throw new AuraHandledException('Erreur lors de la récupération de la fiche : ' + e.getMessage());
		}
	}
}