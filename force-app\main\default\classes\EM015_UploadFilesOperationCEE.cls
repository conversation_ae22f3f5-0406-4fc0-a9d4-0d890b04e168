/**
 * @description       : 
 * <AUTHOR> 
 * @group             : 
 * @last modified on  : 06/04/2023
 * @last modified by  : TOUHAMA AHMED
 * Modifications Log 
 * Ver   Date         Author         Modification

**/
public with sharing class EM015_UploadFilesOperationCEE {
    
    /**
	 * <AUTHOR> AHMED
	 * @date Création 06/04/2023
	 * @description methode used to get documents related to ficheCEE
	 */
	public static List<UploadFilesOperationCEE__mdt> getDocumentsByFicheCEEAndTypeClient(String FicheCEE,String TypeClient,String TypePersonne, String Categorie){
	 	return DM015_UploadFilesOperationCEE.getDocumentsByFicheCEEAndTypeClient(FicheCEE, TypeClient, TypePersonne, Categorie);
	}
}