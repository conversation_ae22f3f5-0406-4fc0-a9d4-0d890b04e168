/**
 * @description       : 
 * <AUTHOR> <PERSON>
 * @group             : 
 * @last modified on  : 06-04-2023
 * @last modified by  : <PERSON>HA<PERSON>
 * Modifications Log 
 * Ver   Date         Author         Modification
 * 1.0   06-04-2023   Ahmed TOUHAMA   Initial Version
**/

public with sharing class DM015_UploadFilesOperationCEE {

    public static List<UploadFilesOperationCEE__mdt> getDocumentsByFicheCEEAndTypeClient(
        String FicheCEE,
        String TypeClient,
        String TypePersonne,
        String Categorie
    ) {
        List<UploadFilesOperationCEE__mdt> results = [
            SELECT Id, AcceptedFormats__c, AllowMultipleFiles__c, FileUploadLabel__c,
                   FlowInterviewGuid__c, OverriddenFileName__c, RelatedRecordId__c, SetVisibilityToAllUser__c,
                   ShowExistingFiles__c, ShowFilesBelow__c, FicheCEE__c, typeClient__c,
                   fileOrder__c, required__c, typeCategorie__c, onlyBackOffice__c, type<PERSON><PERSON>__c
            FROM UploadFilesOperationCEE__mdt
            WHERE FicheCEE__c = :FicheCEE
              AND (typeClient__c = :TypeClient OR typeClient__c = 'client')
        ];

        // Ajout de filtrage côté Apex car les conditions `OR field = :null` ne fonctionnent pas bien en SOQL
        List<UploadFilesOperationCEE__mdt> filteredResults = new List<UploadFilesOperationCEE__mdt>();

        for (UploadFilesOperationCEE__mdt rec : results) {
            Boolean matchPersonne = (TypePersonne == null || rec.typePersonne__c == null || rec.typePersonne__c == TypePersonne);
            Boolean matchCategorie = (Categorie == null || rec.typeCategorie__c == null || rec.typeCategorie__c == Categorie);

            if (matchPersonne && matchCategorie) {
                filteredResults.add(rec);
            }
        }

        return filteredResults;
    }
}