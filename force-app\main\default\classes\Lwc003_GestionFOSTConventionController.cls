/**
 * @description : Contrôleur pour la gestion des conditions spécifiques selon le type de contrat - MODIFIÉ
 * <AUTHOR> Amina BENABDELKRIM
 * @group : D&A Technologies
 * @last modified on : 18-07-2025
 * @last modified by : Amina BENABDELKRIM
 * Modifications Log
 * Ver   Date         Author                 Modification
 * 1.0   03-07-2025   Amina BENABDELKRIM    Initial Version
 */
public with sharing class Lwc003_GestionFOSTConventionController {
    
    private static final String TYPE_APAF = 'Apporteur d\'Affaires (APAF)';
    private static final String TYPE_MOA = 'MOA (Bénéficiaire)';
    private static final String TYPE_MOA_GROUPE = 'MOA Groupe - convention cadre';
    private static final String TYPE_MOE = 'MOE (installateur)';
    private static final String TYPE_MOE_GROUPE = 'MOE Groupe';
    
    private static final String TYPE_COMMISSION = 'Commission';
    private static final String TYPE_FORFAIT = 'Forfait';
    private static final String TYPE_PRIX = 'Prix';
    
   
    
    /**
     * Récupère les conditions spécifiques associées à un contrat
     * @param contractId ID du contrat
     * @return Liste des conditions spécifiques avec relations Dossier et Opportunité
     */
    @AuraEnabled(cacheable=true)
    public static List<ConditionSpecifique__c> getConditionsSpecifiques(Id contractId) {
        try {
           return EM001_ConditionSpecifique.getCSByConvention(contractId);
        } catch (Exception e) {
            System.debug('Erreur lors de la récupération des conditions spécifiques: ' + e.getMessage());
            throw new AuraHandledException('Erreur lors de la récupération des conditions: ' + e.getMessage());
        }
    }
    
    /**
     * Récupère toutes les conditions spécifiques associées au contrat initial et ses versions
     * @param contractId ID du contrat actuel
     * @return Liste des conditions spécifiques de toutes les versions
     */
    @AuraEnabled(cacheable=true)
    public static List<ConditionSpecifique__c> getAllConditionsSpecifiquesForValidation(Id contractId) {
        try {
            List<Contract> currentContract =EM006_Contract.getContractById(contractId);
            
            Id parentConventionId = currentContract[0].ConventionOriginal__c;
            Id conventionId = contractId;
            
            return EM001_ConditionSpecifique.getAllCSOfConvention(parentConventionId, conventionId);
            
            
        } catch (Exception e) {
            System.debug('Erreur lors de la récupération des conditions pour validation: ' + e.getMessage());
            throw new AuraHandledException('Erreur lors de la validation: ' + e.getMessage());
        }
    }
    
    /**
     *  Valide si un dossier ou une opportunité existe déjà dans les conditions spécifiques
     * @param contractId ID du contrat actuel
     * @param dossierId ID du dossier à vérifier (optionnel)
     * @param opportuniteId ID de l'opportunité à vérifier (optionnel)
     * @return Wrapper avec le résultat de la validation
     */
    @AuraEnabled
    public static ValidationResult validateDossierOpportuniteUnique(Id contractId, Id dossierId, Id opportuniteId) {
        try {
            ValidationResult result = new ValidationResult();
            result.isValid = true;
            result.errorMessage = '';
            
            if (dossierId == null && opportuniteId == null) {
                return result;
            }
            
            List<ConditionSpecifique__c> existingConditions = getAllConditionsSpecifiquesForValidation(contractId);
            
            for (ConditionSpecifique__c condition : existingConditions) {
                if (dossierId != null && condition.Dossier__c == dossierId) {
                    result.isValid = false;
                    result.errorMessage = 'Ce dossier "' + condition.Dossier__r.Name + 
                                        '" est déjà associé à une condition spécifique dans une version précédente du contrat (Version ' + 
                                        condition.Convention__r.Version__c + ')';
                    return result;
                }
                
                if (opportuniteId != null && condition.Opportunite__c == opportuniteId) {
                    result.isValid = false;
                    result.errorMessage = 'Cette opportunité "' + condition.Opportunite__r.Name + 
                                        '" est déjà associée à une condition spécifique dans une version précédente du contrat (Version ' + 
                                        condition.Convention__r.Version__c + ')';
                    return result;
                }
            }
            
            return result;
            
        } catch (Exception e) {
            System.debug('Erreur lors de la validation: ' + e.getMessage());
            ValidationResult errorResult = new ValidationResult();
            errorResult.isValid = false;
            errorResult.errorMessage = 'Erreur lors de la validation: ' + e.getMessage();
            return errorResult;
        }
    }
    
    /**
     * Sauvegarde une liste de conditions spécifiques
     * @param conditions Liste des conditions à sauvegarder
     * @return Liste des conditions sauvegardées
     */
    @AuraEnabled
    public static List<ConditionSpecifique__c> saveConditionsSpecifiques(List<ConditionSpecifique__c> conditions, String typeContract) {
        try {
            system.debug('this is typeContract==>  '+typeContract);
            if (conditions == null || conditions.isEmpty()) {
                throw new AuraHandledException('Aucune condition à sauvegarder');
            }

            insert conditions;
            
            updateRelatedRecords(conditions, typeContract);
            
            System.debug('Conditions spécifiques sauvegardées avec succès: ' + conditions.size());
            return conditions;
            
        } catch (DmlException e) {
            System.debug('Erreur DML lors de la sauvegarde: ' + e.getMessage());
            throw new AuraHandledException('Erreur lors de la sauvegarde: ' + e.getDmlMessage(0));
        } catch (Exception e) {
            System.debug('Erreur lors de la sauvegarde des conditions: ' + e.getMessage());
            throw new AuraHandledException('Erreur lors de la sauvegarde: ' + e.getMessage());
        }
    }
     
    /**
     * Met à jour les enregistrements liés après création des conditions
     * @param newConditions Liste des nouvelles conditions créées
     */
   private static void updateRelatedRecords(List<ConditionSpecifique__c> newConditions, String typeContract) {
    try {
        List<Dossier__c> dossiersToUpdate = new List<Dossier__c>();
        List<Opportunity> opportunitiesToUpdate = new List<Opportunity>();
        
        // Pour les dossiers APAF, on doit d'abord récupérer les enregistrements existants
        Map<Id, Dossier__c> existingDossiers = new Map<Id, Dossier__c>();
        Map<Id, Opportunity> existingOpportunities = new Map<Id, Opportunity>();
        
        if (typeContract == 'Apporteur d\'Affaires (APAF)') {
            // Récupération des dossiers existants pour vérifier les champs APAF
            Set<Id> dossierIds = new Set<Id>();
            Set<Id> opportunityIds = new Set<Id>();
            
            for (ConditionSpecifique__c condition : newConditions) {
                if (condition.Dossier__c != null) {
                    dossierIds.add(condition.Dossier__c);
                }
                if (condition.Opportunite__c != null) {
                    opportunityIds.add(condition.Opportunite__c);
                }
            }
            
            if (!dossierIds.isEmpty()) {
                existingDossiers = new Map<Id, Dossier__c>([
                    SELECT Id, ConditionSpecifiqueAPAF1__c, ConditionSpecifiqueAPAF2__c
                    FROM Dossier__c 
                    WHERE Id IN :dossierIds
                ]);
            }
            
            if (!opportunityIds.isEmpty()) {
                existingOpportunities = new Map<Id, Opportunity>([
                    SELECT Id, ConditionSpecifiqueAPAF1__c, ConditionSpecifiqueAPAF2__c
                    FROM Opportunity 
                    WHERE Id IN :opportunityIds
                ]);
            }
        }
        
        for (ConditionSpecifique__c condition : newConditions) {
            if (condition.Dossier__c != null) {
                Dossier__c dossier = new Dossier__c();
                dossier.Id = condition.Dossier__c;
                
                if (typeContract == 'Apporteur d\'Affaires (APAF)') {
                    Dossier__c existingDossier = existingDossiers.get(condition.Dossier__c);
                    
                    if (existingDossier != null) {
                        if (existingDossier.ConditionSpecifiqueAPAF1__c != null) {
                            dossier.ConditionSpecifiqueAPAF2__c = condition.Id;
                        } else {
                            dossier.ConditionSpecifiqueAPAF1__c = condition.Id;
                        }
                    } else {
                        dossier.ConditionSpecifiqueAPAF1__c = condition.Id;
                    }
                } else {
                    dossier.ConditionSpecifique__c = condition.Id;
                }
                dossiersToUpdate.add(dossier);
            }
            
            if (condition.Opportunite__c != null) {
                Opportunity opp = new Opportunity();
                opp.Id = condition.Opportunite__c;
                
                if (typeContract == 'Apporteur d\'Affaires (APAF)') {
                    Opportunity existingOpp = existingOpportunities.get(condition.Opportunite__c);
                    
                    if (existingOpp != null) {
                        if (existingOpp.ConditionSpecifiqueAPAF1__c != null) {
                            opp.ConditionSpecifiqueAPAF2__c = condition.Id;
                        } else {
                            // APAF1 n'est pas rempli, affecter à APAF1
                            opp.ConditionSpecifiqueAPAF1__c = condition.Id;
                        }
                    } else {
                        // Si pas trouvé, affecter à APAF1 par défaut
                        opp.ConditionSpecifiqueAPAF1__c = condition.Id;
                    }
                } else {
                    opp.ConditionSpecifique__c = condition.Id;
                }
                opportunitiesToUpdate.add(opp);
            }
        }
        
        if (!dossiersToUpdate.isEmpty()) {
            update dossiersToUpdate;
            System.debug('Dossiers mis à jour avec les conditions: ' + dossiersToUpdate.size());
        }
        
        if (!opportunitiesToUpdate.isEmpty()) {
            update opportunitiesToUpdate;
            System.debug('Opportunités mises à jour avec les conditions: ' + opportunitiesToUpdate.size());
        }
        
    } catch (Exception e) {
        System.debug('Erreur lors de la mise à jour des enregistrements liés: ' + e.getMessage());
    }
}
    
    /**
     * Supprime une condition spécifique
     * @param conditionSpecifiqueId ID de la condition spécifique à supprimer
     */
    @AuraEnabled
    public static void deleteConditionSpecifique(Id conditionSpecifiqueId) {
        try {
            if (conditionSpecifiqueId == null) {
                throw new AuraHandledException('ID de la condition non fourni');
            }
            
            ConditionSpecifique__c conditionToDelete =EM001_ConditionSpecifique.getCSById(conditionSpecifiqueId);
                       
            delete conditionToDelete;
            
            System.debug('Condition spécifique supprimée avec succès: ' + conditionSpecifiqueId);
            
        } catch (Exception e) {
            System.debug('Erreur lors de la suppression de la condition: ' + e.getMessage());
            throw new AuraHandledException('Erreur lors de la suppression: ' + e.getMessage());
        }
    }
    
    /**
     * Récupère le nom d'un dossier
     * @param dossierId ID du dossier
     * @return Nom du dossier
     */
    @AuraEnabled(cacheable=true)
    public static String getDossierName(Id dossierId) {
        try {
            if (dossierId == null) {
                return null;
            }
            
            Dossier__c dossier =EM020_Dossier.getDossierById(dossierId);
            return dossier.Name;
            
        } catch (QueryException e) {
            System.debug('Dossier non trouvé: ' + dossierId);
            return null;
        } catch (Exception e) {
            System.debug('Erreur lors de la récupération du nom du dossier: ' + e.getMessage());
            return null;
        }
    }
    
    /**
     * Récupère le nom d'une opportunité
     * @param opportunityId ID de l'opportunité
     * @return Nom de l'opportunité
     */
    @AuraEnabled(cacheable=true)
    public static String getOpportunityName(Id opportunityId) {
        try {
            if (opportunityId == null) {
                return null;
            }
            
            Opportunity opportunity = EM004_Opportunity.getOpportunityById(opportunityId);
            return opportunity.Name;
            
        } catch (QueryException e) {
            System.debug('Opportunité non trouvée: ' + opportunityId);
            return null;
        } catch (Exception e) {
            System.debug('Erreur lors de la récupération du nom de l\'opportunité: ' + e.getMessage());
            return null;
        }
    }

    /**
     * Récupère les opérations associées à un dossier
     * @param dossierId ID du dossier
     * @return Liste des opérations avec leur nom de fiche
     */
    @AuraEnabled(cacheable=true)
    public static List<Operation__c> getOperationsByDossier(Id dossierId) {
        try {
            if (dossierId == null) {
                return new List<Operation__c>();
            }
            
            List<Operation__c> operations = EM010_OperationCEE.getOperationByDossier(dossierId);
            
            System.debug('Opérations trouvées pour le dossier ' + dossierId + ': ' + operations.size());
            return operations;
            
        } catch (QueryException e) {
            System.debug('Erreur lors de la récupération des opérations: ' + e.getMessage());
            return new List<Operation__c>();
        } catch (Exception e) {
            System.debug('Erreur lors de la récupération des opérations: ' + e.getMessage());
            return new List<Operation__c>();
        }
    }
    
    /**
     *  Récupère les types de travaux associés à une opportunité
     * @param opportunityId ID de l'opportunité
     * @return Liste des types de travaux (noms des produits)
     */
 @AuraEnabled(cacheable=true)
public static List<String> getTypeTravauxByOpportunity(Id opportunityId) {
    try {
        if (opportunityId == null) {
            return new List<String>();
        }
        
        List<ChiffrageLineItem__c> chiffrageLines = EM009_ChiffrageLineItem.getCLIByOpportunity(opportunityId);
        
        if (chiffrageLines == null || chiffrageLines.isEmpty()) {
            return new List<String>();
        }
        
        // SOLUTION 1: Utilisation d'un Set directement sur les valeurs nettoyées
        Set<String> typeTravauxSet = new Set<String>();
        
        for (ChiffrageLineItem__c line : chiffrageLines) {
            if (line.Product__r != null && String.isNotBlank(line.Product__r.Name)) {
                String productName = line.Product__r.Name.replaceAll('\\s+', ' ').trim();
                
                if (String.isNotBlank(productName)) {
                    typeTravauxSet.add(productName);
                }
            }
        }
        
        // Conversion en List et tri
        List<String> typeTravauxList = new List<String>(typeTravauxSet);
        typeTravauxList.sort();
        
        System.debug('Types de travaux uniques pour opportunité ' + opportunityId + ': ' + typeTravauxList);
        System.debug('Nombre d\'éléments uniques: ' + typeTravauxList.size());
        
        return typeTravauxList;
        
    } catch (Exception e) {
        System.debug('Erreur: ' + e.getMessage());
        return new List<String>();
    }
}
    
    /**
     *  Classe wrapper pour les résultats de validation
     */
    public class ValidationResult {
        @AuraEnabled
        public Boolean isValid { get; set; }
        
        @AuraEnabled
        public String errorMessage { get; set; }
        
        public ValidationResult() {
            this.isValid = true;
            this.errorMessage = '';
        }
    }
}