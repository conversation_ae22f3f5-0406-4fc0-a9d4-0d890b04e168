/**
* @File Name : EM007_GrilleTarifaire
* @Description : DM de l'objet GrilleTarifaire__c
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 10, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 10, 2025|   | Initial Version
**/
public with sharing class EM007_GrilleTarifaire {
     /**
    * @Description      :   Cette methode permet de récupérer les grilles tarifaires associé à une convention 
    * @method Name      :   getGrilleTarifaireByConvention
    * <AUTHOR>   Amina <PERSON>ENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   conventionId => Id du convention
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<GrilleTarifaire__c> getGrilleTarifaireByConvention(Id conventionId){
          return DM007_GrilleTarifaire.selectGrilleTarifaireByConvention(conventionId);     
    }

     /**
    * @Description      :   Cette methode permet de récupérer les grilles tarifaire par son Id 
    * @method Name      :   getGrilleTarifaireById
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   grilleId => Id de la grille tarifaire
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static GrilleTarifaire__c getGrilleTarifaireById(Id grilleId){
         return DM007_GrilleTarifaire.selectGrilleTarifaireById(grilleId);     

    }
}