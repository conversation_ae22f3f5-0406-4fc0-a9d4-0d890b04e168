/**
* @File Name : Lwc002_CreateAvenantController
* @Description : CONTROLLEUR DU COMPOSANT Lwc002_CreateAvenant
* <AUTHOR> Amina BENABDELKRIM
* @Last Modified By :
* @Last Modified On : JULY 07, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | JULY 05, 2025 |   | Initial Version
* 1.1 | JULY 09, 2025 |   | Ajout le controle de clonage à partir du dernier avenant ou de la convention initiale
**/
public with sharing class Lwc002_CreateAvenantController {
    private static final String CONVENTION_API_NAME = 'Contract';

    
    /**
     * Clone une convention pour créer un avenant
     */
    @AuraEnabled
    public static Id createConvention(Id conventionId) {
        try {
            String dynamicQuery = SM003_ObjectDescriber.buildDynamicQuery(CONVENTION_API_NAME, conventionId);
            
            System.debug('Requête dynamique générée: ' + dynamicQuery);
            
            List<SObject> results = Database.query(dynamicQuery);
            
            if (results.isEmpty()) {
                System.debug('Convention non trouvée avec l\'ID: ' + conventionId);
                throw new System.QueryException('Convention non trouvée avec l\'ID: ' + conventionId);
            }
            
            SObject ConventionData = results[0];
            
            SObject ConventionAfterClone = ConventionData.clone(false, true, false, false);
            
            String originalName = (String)ConventionAfterClone.get('Name');
            ConventionAfterClone.put('TECH_isAvenant__c', true);
            ConventionAfterClone.put('ID_convention__c', null);
            ConventionAfterClone.put('Id_Sharepoint__c', null);
            ConventionAfterClone.put('Status', 'Brouillon');
            ConventionAfterClone.put('StatutApprobationV2__c', '');
            
            Decimal originalVersion = (Decimal)ConventionData.get('Version__c');
            if (originalVersion == null) {
                originalVersion = 0;
            }
            Decimal newVersion = originalVersion + 1;
            ConventionAfterClone.put('Version__c', newVersion);
            if(newVersion == 1){
                ConventionAfterClone.put('ConventionOriginal__c', conventionId);
            }
            ConventionAfterClone.put('Name', originalName + ' - V' + newVersion);
            
            System.debug('Version originale: ' + originalVersion + ', Nouvelle version: ' + newVersion);
            
            // Insertion du contrat cloné
            insert ConventionAfterClone;
            
            // Clonage des grilles tarifaires associées
            cloneGrillesTarifaires(conventionId, ConventionAfterClone.Id);
            
            SObject originalConvention = ConventionData.clone(true, false, false, false);
            // originalConvention.put('Status', 'Annulée');
            // update originalConvention;
            
            System.debug('Avenant créé avec succès. Nouvel ID: ' + ConventionAfterClone.Id);
            
            return ConventionAfterClone.Id;
            
        } catch (DmlException e) {
            System.debug('Erreur DML lors de la création de l\'avenant: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw new AuraHandledException('Erreur lors de la création de l\'avenant: ' + e.getMessage());
        } catch (Exception e) {
            System.debug('Erreur générale lors de la création de l\'avenant: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw new AuraHandledException('Erreur lors de la création de l\'avenant: ' + e.getMessage());
        }
    }
    private static void cloneGrillesTarifaires(Id originalConventionId, Id newConventionId) {
        try {
            List<GrilleTarifaire__c> originalGrilles=EM007_GrilleTarifaire.getGrilleTarifaireByConvention(originalConventionId);
            
            System.debug('Nombre de grilles tarifaires trouvées: ' + originalGrilles.size());
            
            if (!originalGrilles.isEmpty()) {
                List<GrilleTarifaire__c> grillesClonees = new List<GrilleTarifaire__c>();
                
                for (GrilleTarifaire__c grille : originalGrilles) {
                    GrilleTarifaire__c grilleClonee = grille.clone(false, true, false, false);
                    
                    grilleClonee.Convention__c = newConventionId;
                    
                    if (grilleClonee.Name != null) {
                        grilleClonee.Name = grilleClonee.Name + ' - Copie';
                    }
                    
                    grillesClonees.add(grilleClonee);
                }
                
                insert grillesClonees;
                
                System.debug('Grilles tarifaires clonées avec succès. Nombre: ' + grillesClonees.size());
            }
            
        } catch (Exception e) {
            System.debug('Erreur lors du clonage des grilles tarifaires: ' + e.getMessage());
            System.debug('Stack trace: ' + e.getStackTraceString());
            throw new AuraHandledException('Erreur lors du clonage des grilles tarifaires: ' + e.getMessage());
        }
    }
    /**
     * Récupère les informations de la convention pour l'affichage
     */
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> getConventionInfo(Id conventionId) {
        try {
            List<Contract> results =EM006_Contract.getContractById(conventionId); 
            
            if (results.isEmpty()) {
                throw new System.QueryException('Convention non trouvée avec l\'ID: ' + conventionId);
            }
            
            SObject convention = results[0];
            Map<String, Object> info = new Map<String, Object>();
            info.put('id', convention.Id);
            info.put('name', convention.get('Name'));
            info.put('ContractNumber', convention.get('ContractNumber'));

            return info;
            
        } catch (Exception e) {
            System.debug('Erreur lors de la récupération de la convention: ' + e.getMessage());
            throw new AuraHandledException('Erreur lors de la récupération de la convention: ' + e.getMessage());
        }
    }

    /**
     * Vérifie s'il existe une version supérieure à la version actuelle
     */
    @AuraEnabled(cacheable=true)
    public static Map<String, Object> checkExistingVersions(Id conventionId) {
        try {
            List<Contract> currentConvention = EM006_Contract.getContractById(conventionId);
            
            if (currentConvention.isEmpty()) {
                throw new System.QueryException('Convention non trouvée avec l\'ID: ' + conventionId);
            }
            
            Contract current = currentConvention[0];
            Decimal currentVersion = current.Version__c != null ? current.Version__c : 0;
            
            Id originalConventionId = current.ConventionOriginal__c != null ? 
                                    current.ConventionOriginal__c : conventionId;
            
            List<Contract> higherVersions = EM006_Contract.gethigherVersionOfContract(originalConventionId,currentVersion);
            
            Map<String, Object> result = new Map<String, Object>();
            result.put('hasHigherVersions', !higherVersions.isEmpty());
            result.put('currentVersion', currentVersion);
            result.put('canCreateAvenant', higherVersions.isEmpty());
            
            if (!higherVersions.isEmpty()) {
                List<Map<String, Object>> versionsInfo = new List<Map<String, Object>>();
                for (Contract version : higherVersions) {
                    Map<String, Object> versionInfo = new Map<String, Object>();
                    versionInfo.put('id', version.Id);
                    versionInfo.put('version', version.Version__c);
                    versionInfo.put('name', version.Name);
                    versionInfo.put('status', version.Status);
                    versionsInfo.add(versionInfo);
                }
                result.put('higherVersions', versionsInfo);
                result.put('latestVersion', higherVersions[0].Version__c);
            }
            
            System.debug('Résultat de la vérification des versions: ' + result);
            return result;
            
        } catch (Exception e) {
            System.debug('Erreur lors de la vérification des versions: ' + e.getMessage());
            throw new AuraHandledException('Erreur lors de la vérification des versions: ' + e.getMessage());
        }
    }
        
}