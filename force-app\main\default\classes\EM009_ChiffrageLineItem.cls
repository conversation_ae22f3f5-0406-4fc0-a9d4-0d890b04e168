/**
* @File Name : EM009_ChiffrageLineItem
* @Description : EM de l'objet ChiffrageLineItem__c
* <AUTHOR> BENABDELKRIM
* @Last Modified By :
* @Last Modified On : July 21, 2025
* @Modification Log :
*==============================================================================
* Ver | Date | Author | Modification
*==============================================================================
* 1.0 | July 21, 2025|   | Initial Version
**/
public with sharing class EM009_ChiffrageLineItem {
     /**
    * @Description      :   Cette methode permet récupérer les chiffrage line item asscoia a une opportunité
    * @method Name      :   selectCLIByOpportunity
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 21, 2025
    * @Param            :   opportunityIds => Id de l'opportunité
    * @Modification Log :
    * 1.0       			July 21, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
   public static List<ChiffrageLineItem__c> getCLIByOpportunity(Set<Id> opportunityIds){
        return DM009_ChiffrageLineItem.selectCLIByOpportunity(opportunityIds); 
    }
        /**
    * @Description      :   Cette methode permet récupérer les chiffrages line item par opportunité
    * <AUTHOR>   Amina BENABDELKRIM 
    * @date             :   July 10, 2025
    * @Param            :   opportunityId => Id de l'opportunité
    * @Modification Log :
    * 1.0       			July 10, 2025				Amina BENABDELKRIM - D&A Technologies   Création
    * -------------------------------------------------------------------------------------------------
    **/
    public static List<ChiffrageLineItem__c> getCLIByOpportunity(Id opportunityId){
        return DM009_ChiffrageLineItem.selectCLIByOpportunity(opportunityId);  
    }
}